<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../utils/SecurityUtils.php';
require_once __DIR__ . '/../utils/EmailUtils.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    // Validate required fields
    $required_fields = ['name', 'email', 'password', 'company_name'];
    foreach ($required_fields as $field) {
        if (empty($input[$field])) {
            throw new Exception("Field '{$field}' is required");
        }
    }
    
    // Sanitize inputs
    $name = SecurityUtils::sanitizeInput($input['name']);
    $email = SecurityUtils::sanitizeInput($input['email']);
    $password = $input['password'];
    $company_name = SecurityUtils::sanitizeInput($input['company_name']);
    $company_size = SecurityUtils::sanitizeInput($input['company_size'] ?? '');
    $industry = SecurityUtils::sanitizeInput($input['industry'] ?? '');
    $selected_plan = SecurityUtils::sanitizeInput($input['selected_plan'] ?? 'basic');
    
    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Invalid email format');
    }
    
    // Validate password strength
    if (!SecurityUtils::validatePasswordStrength($password)) {
        throw new Exception('Password does not meet security requirements');
    }
    
    // Connect to database
    $db = Config::getDatabase();
    $conn = new mysqli($db['host'], $db['username'], $db['password'], $db['database']);
    
    if ($conn->connect_error) {
        throw new Exception('Database connection failed');
    }
    
    // Check if email already exists
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        throw new Exception('Email already registered');
    }
    
    // Start transaction
    $conn->begin_transaction();
    
    try {
        // Create company first
        $company_stmt = $conn->prepare("
            INSERT INTO companies (name, size, industry, subscription_plan, created_at) 
            VALUES (?, ?, ?, ?, NOW())
        ");
        $company_stmt->bind_param("ssss", $company_name, $company_size, $industry, $selected_plan);
        $company_stmt->execute();
        $company_id = $conn->insert_id;
        
        // Hash password
        $password_hash = password_hash($password, PASSWORD_DEFAULT);
        
        // Generate email verification token
        $verification_token = bin2hex(random_bytes(32));
        
        // Create user
        $user_stmt = $conn->prepare("
            INSERT INTO users (name, email, password, company_id, role, email_verified, verification_token, created_at) 
            VALUES (?, ?, ?, ?, 'admin', 0, ?, NOW())
        ");
        $user_stmt->bind_param("sssis", $name, $email, $password_hash, $company_id, $verification_token);
        $user_stmt->execute();
        $user_id = $conn->insert_id;
        
        // Create subscription record
        $subscription_stmt = $conn->prepare("
            INSERT INTO subscriptions (company_id, plan_id, status, created_at, updated_at) 
            VALUES (?, ?, 'trial', NOW(), NOW())
        ");
        $subscription_stmt->bind_param("is", $company_id, $selected_plan);
        $subscription_stmt->execute();
        
        // Commit transaction
        $conn->commit();
        
        // Send verification email (if email utils are available)
        if (class_exists('EmailUtils')) {
            try {
                $verification_link = "http://" . $_SERVER['HTTP_HOST'] . "/verify-email.php?token=" . $verification_token;
                EmailUtils::sendVerificationEmail($email, $name, $verification_link);
            } catch (Exception $e) {
                // Log email error but don't fail registration
                error_log("Email verification failed: " . $e->getMessage());
            }
        }
        
        // Log successful registration
        error_log("New user registered: {$email} for company: {$company_name}");
        
        echo json_encode([
            'success' => true,
            'message' => 'Registration successful! Please check your email to verify your account.',
            'user' => [
                'id' => $user_id,
                'name' => $name,
                'email' => $email,
                'company_id' => $company_id,
                'company_name' => $company_name,
                'role' => 'admin',
                'email_verified' => false
            ]
        ]);
        
    } catch (Exception $e) {
        $conn->rollback();
        throw $e;
    }
    
    $conn->close();
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
    error_log("Registration error: " . $e->getMessage());
}
?>
