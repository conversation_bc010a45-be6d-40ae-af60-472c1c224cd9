<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../utils/SecurityUtils.php';
require_once __DIR__ . '/../utils/EmailUtils.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    // Validate required fields
    $required_fields = ['name', 'email', 'password', 'company_name'];
    foreach ($required_fields as $field) {
        if (empty($input[$field])) {
            throw new Exception("Field '{$field}' is required");
        }
    }
    
    // Sanitize inputs
    $name = SecurityUtils::sanitizeInput($input['name']);
    $email = SecurityUtils::sanitizeInput($input['email']);
    $password = $input['password'];
    $company_name = SecurityUtils::sanitizeInput($input['company_name']);
    $company_size = SecurityUtils::sanitizeInput($input['company_size'] ?? '');
    $industry = SecurityUtils::sanitizeInput($input['industry'] ?? '');
    $selected_plan = SecurityUtils::sanitizeInput($input['selected_plan'] ?? 'basic');
    
    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Invalid email format');
    }
    
    // Validate password strength
    if (!SecurityUtils::validatePasswordStrength($password)) {
        throw new Exception('Password does not meet security requirements');
    }
    
    // Connect to database
    $db = Config::getDatabase();
    $conn = new mysqli($db['host'], $db['username'], $db['password'], $db['database']);
    
    if ($conn->connect_error) {
        throw new Exception('Database connection failed');
    }
    
    // Check if email already exists
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        throw new Exception('Email already registered');
    }
    
    // Start transaction
    $conn->begin_transaction();
    
    try {
        // Generate unique IDs
        $company_object_id = 'comp_' . time() . '_' . rand(100, 999);
        $user_object_id = 'user_' . time() . '_' . rand(100, 999);

        // Hash password
        $password_hash = password_hash($password, PASSWORD_DEFAULT);

        // Generate email verification token
        $verification_token = bin2hex(random_bytes(32));

        // Create user first (without company_id initially)
        $user_stmt = $conn->prepare("
            INSERT INTO users (object_id, name, email, password_hash, role, email_verified, verification_token, created_at)
            VALUES (?, ?, ?, ?, 'admin', 0, ?, NOW())
        ");
        $user_stmt->bind_param("sssss", $user_object_id, $name, $email, $password_hash, $verification_token);
        $user_stmt->execute();
        $user_id = $conn->insert_id;

        // Create company with owner_id
        $company_stmt = $conn->prepare("
            INSERT INTO companies (object_id, name, size, industry, subscription_plan, owner_id, created_at)
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        $company_stmt->bind_param("ssssss", $company_object_id, $company_name, $company_size, $industry, $selected_plan, $user_object_id);
        $company_stmt->execute();
        $company_id = $conn->insert_id;

        // Update user with company_id
        $update_user_stmt = $conn->prepare("UPDATE users SET company_id = ? WHERE object_id = ?");
        $update_user_stmt->bind_param("ss", $company_object_id, $user_object_id);
        $update_user_stmt->execute();
        
        // Create subscription record
        $subscription_object_id = 'sub_' . time() . '_' . rand(100, 999);
        $trial_end_date = date('Y-m-d H:i:s', strtotime('+14 days'));
        $subscription_stmt = $conn->prepare("
            INSERT INTO subscriptions (object_id, company_id, user_id, plan_id, plan_name, status, start_date, trial_end_date, billing_cycle, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, 'trial', NOW(), ?, ?, NOW(), NOW())
        ");
        $billing_cycle = isset($input['billing_cycle']) ? $input['billing_cycle'] : 'monthly';
        $subscription_stmt->bind_param("sssssss", $subscription_object_id, $company_object_id, $user_object_id, $selected_plan, $selected_plan, $trial_end_date, $billing_cycle);
        $subscription_stmt->execute();
        
        // Commit transaction
        $conn->commit();
        
        // Send verification email (if email utils are available)
        if (class_exists('EmailUtils')) {
            try {
                $verification_link = "http://" . $_SERVER['HTTP_HOST'] . "/verify-email.php?token=" . $verification_token;
                EmailUtils::sendVerificationEmail($email, $name, $verification_link);
            } catch (Exception $e) {
                // Log email error but don't fail registration
                error_log("Email verification failed: " . $e->getMessage());
            }
        }
        
        // Log successful registration
        error_log("New user registered: {$email} for company: {$company_name}");
        
        echo json_encode([
            'success' => true,
            'message' => 'Registration successful! Please check your email to verify your account.',
            'user' => [
                'id' => $user_id,
                'object_id' => $user_object_id,
                'name' => $name,
                'email' => $email,
                'company_id' => $company_object_id,
                'company_name' => $company_name,
                'role' => 'admin',
                'email_verified' => false
            ]
        ]);
        
    } catch (Exception $e) {
        $conn->rollback();
        throw $e;
    }
    
    $conn->close();
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
    error_log("Registration error: " . $e->getMessage());
}
?>
