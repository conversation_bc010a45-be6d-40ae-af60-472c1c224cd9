function ContractTemplate({ contract, companyInfo }) {
    try {
        // Check for customer data and handle if it's just an ID
        const [customerData, setCustomerData] = React.useState(null);
        const [loading, setLoading] = React.useState(true);

        // Extract template settings from companyInfo
        const templates = companyInfo && companyInfo.templates ? companyInfo.templates : {
            paperSize: 'a4',
            orientation: 'portrait',
            margins: { top: 15, right: 15, bottom: 15, left: 15 },
            contractTemplate: 'professional',
            headerColor: '#3b82f6',
            accentColor: '#1e3a8a',
            fontFamily: 'Segoe UI, sans-serif',
            fontSize: 'medium',
            showLogo: true,
            showSignature: true,
            showWatermark: true
        };

        React.useEffect(() => {
            async function fetchCustomerData() {
                try {
                    const contractData = contract.objectData || contract;
                    if (typeof contractData.customer === 'string' || contractData.customer instanceof String) {
                        // If customer is just an ID, fetch the customer data
                        const customer = await trickleGetObject('customer', contractData.customer);
                        setCustomerData(customer.objectData);
                    } else {
                        // If customer is already an object
                        setCustomerData(contractData.customer);
                    }
                } catch (error) {
                    console.error('Error fetching customer data:', error);
                } finally {
                    setLoading(false);
                }
            }
            
            fetchCustomerData();
        }, [contract]);

        // Format status text with first letter capitalized
        const getStatusText = (status) => {
            if (!status) return 'Draft';
            return status.charAt(0).toUpperCase() + status.slice(1);
        };

        // Format contract type for display
        const getContractTypeText = (type) => {
            if (!type) return 'Service';
            const typeMap = {
                'service': 'Service',
                'product': 'Product',
                'licensing': 'Licensing',
                'partnership': 'Partnership',
                'employment': 'Employment',
                'maintenance': 'Maintenance',
                'digital-marketing': 'Digital Marketing'
            };
            return typeMap[type.toLowerCase()] || type.charAt(0).toUpperCase() + type.slice(1);
        };

        // Apply template settings to style
        const getTemplateStyles = () => {
            let style = {};
            
            // Font family
            if (templates.fontFamily) {
                style.fontFamily = templates.fontFamily;
            }
            
            // Font size
            const fontSizeMap = {
                'small': '0.875rem',
                'medium': '1rem',
                'large': '1.125rem'
            };
            style.fontSize = fontSizeMap[templates.fontSize] || '1rem';
            
            // Colors
            style.headerColor = templates.headerColor || '#3b82f6';
            style.accentColor = templates.accentColor || '#1e3a8a';
            
            // Paper size
            const paperSizeClass = templates.paperSize || 'a4';
            const orientationClass = templates.orientation || 'portrait';
            
            return {
                style,
                paperSizeClass,
                orientationClass
            };
        };

        const { style, paperSizeClass, orientationClass } = getTemplateStyles();

        if (loading) {
            return (
                <div className="flex justify-center items-center p-8">
                    <i className="fas fa-spinner fa-spin fa-2x text-blue-500"></i>
                </div>
            );
        }

        const contractData = contract.objectData || contract;
        
        // Get status for watermark
        const isDraft = !contractData.status || contractData.status.toLowerCase() === 'draft';
        const isSigned = contractData.status && contractData.status.toLowerCase() === 'signed';
        const isPending = contractData.status && contractData.status.toLowerCase() === 'pending';
        
        // Format billing cycle text
        const getBillingCycleText = (cycle) => {
            if (!cycle) return 'Monthly';
            const cycleMap = {
                'monthly': 'Monthly',
                'quarterly': 'Quarterly',
                'biannually': 'Bi-annually',
                'annually': 'Annually'
            };
            return cycleMap[cycle.toLowerCase()] || cycle.charAt(0).toUpperCase() + cycle.slice(1);
        };

        return (
            <div 
                data-name="contract-template" 
                className={`template-container ${paperSizeClass}-page ${orientationClass}`}
                style={style}
            >
                {templates.showWatermark && isDraft && <div className="watermark">DRAFT</div>}
                {templates.showWatermark && isPending && <div className="watermark">PENDING</div>}
                {templates.showWatermark && isSigned && <div className="watermark">SIGNED</div>}
                
                {/* Header */}
                <div 
                    className="template-header"
                    style={{ borderColor: style.headerColor }}
                >
                    <div>
                        <h1 
                            className="template-title"
                            style={{ color: style.accentColor }}
                        >
                            {getContractTypeText(contractData.type).toUpperCase()} CONTRACT
                        </h1>
                        <p className="template-number">#{contractData.contractNumber || contract.objectId}</p>
                    </div>
                    <div className="company-details">
                        {templates.showLogo && companyInfo.logo && (
                            <img 
                                src={companyInfo.logo} 
                                alt="Company Logo" 
                                className="company-logo"
                            />
                        )}
                        <h2 
                            className="company-name"
                            style={{ color: style.accentColor }}
                        >
                            {companyInfo.companyName}
                        </h2>
                        <p>{companyInfo.companyAddress}</p>
                        <p>{companyInfo.companyPhone}</p>
                        <p>{companyInfo.companyEmail}</p>
                        {companyInfo.companyWebsite && <p>{companyInfo.companyWebsite}</p>}
                        {companyInfo.companyGST && <p>GSTIN: {companyInfo.companyGST}</p>}
                    </div>
                </div>

                {/* Contract Title */}
                <div className="contract-header">
                    <h2 
                        className="text-2xl font-bold text-center mb-2"
                        style={{ color: style.accentColor }}
                    >
                        {contractData.title}
                    </h2>
                    <p className="text-center text-gray-600">Contract #{contractData.contractNumber || contract.objectId}</p>
                    <p className="text-center text-gray-600 mt-2">
                        <span className={`status-badge status-${contractData.status || 'draft'}`}>
                            {getStatusText(contractData.status)}
                        </span>
                    </p>
                </div>

                {/* Contract Parties */}
                <div className="contract-section">
                    <h3 
                        className="text-lg font-semibold mb-4"
                        style={{ color: style.accentColor }}
                    >
                        PARTIES TO THIS AGREEMENT
                    </h3>
                    <div className="contract-parties">
                        <div className="party-details">
                            <h4 className="font-medium mb-2">Service Provider:</h4>
                            <p className="mb-1"><strong>{companyInfo.companyName}</strong></p>
                            <p className="mb-1">{companyInfo.companyAddress}</p>
                            <p className="mb-1">Phone: {companyInfo.companyPhone}</p>
                            <p className="mb-1">Email: {companyInfo.companyEmail}</p>
                            {companyInfo.companyGST && <p className="mb-1">GSTIN: {companyInfo.companyGST}</p>}
                            <p>Represented by: <strong>{companyInfo.authorizedName || 'Authorized Representative'}</strong></p>
                        </div>

                        <div className="party-details">
                            <h4 className="font-medium mb-2">Client:</h4>
                            {customerData ? (
                                <div>
                                    <p className="mb-1"><strong>{customerData.name}</strong></p>
                                    {customerData.company && <p className="mb-1">{customerData.company}</p>}
                                    {customerData.address && <p className="mb-1">{customerData.address}</p>}
                                    <p className="mb-1">Phone: {customerData.phone || 'N/A'}</p>
                                    <p className="mb-1">Email: {customerData.email || 'N/A'}</p>
                                    {customerData.gst && <p className="mb-1">GSTIN: {customerData.gst}</p>}
                                </div>
                            ) : (
                                <p className="text-gray-600">Client information not available</p>
                            )}
                        </div>
                    </div>
                </div>

                {/* Contract Details */}
                <div className="contract-section">
                    <h3 
                        className="text-lg font-semibold mb-4"
                        style={{ color: style.accentColor }}
                    >
                        CONTRACT DETAILS
                    </h3>
                    <div className="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <p className="mb-2"><strong>Contract Type:</strong> {getContractTypeText(contractData.type)}</p>
                            <p className="mb-2"><strong>Start Date:</strong> {formatDate(contractData.startDate)}</p>
                            <p className="mb-2"><strong>End Date:</strong> {formatDate(contractData.endDate)}</p>
                        </div>
                        <div>
                            <p className="mb-2"><strong>Contract Value:</strong> {formatCurrency(contractData.value)}</p>
                            {contractData.isRecurring && (
                                <p className="mb-2"><strong>Billing Cycle:</strong> {getBillingCycleText(contractData.billingCycle)}</p>
                            )}
                            <p className="mb-2"><strong>Contract Status:</strong> {getStatusText(contractData.status)}</p>
                        </div>
                    </div>

                    {contractData.description && (
                        <div className="mb-4">
                            <h4 className="font-medium mb-2">Description:</h4>
                            <p className="whitespace-pre-line">{contractData.description}</p>
                        </div>
                    )}
                </div>

                {/* Recurring Services */}
                {contractData.isRecurring && contractData.relatedItems && contractData.relatedItems.length > 0 && (
                    <div className="contract-section">
                        <h3 
                            className="text-lg font-semibold mb-4"
                            style={{ color: style.accentColor }}
                        >
                            RECURRING SERVICES
                        </h3>
                        <table className="items-table">
                            <thead>
                                <tr style={{ backgroundColor: `${style.headerColor}10` }}>
                                    <th>Service Description</th>
                                    <th className="text-center">Qty</th>
                                    <th className="text-right">Rate</th>
                                    <th className="text-right">Frequency</th>
                                    <th className="text-right">Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                {contractData.relatedItems.map((item, index) => (
                                    <tr key={index}>
                                        <td>{item.name}</td>
                                        <td className="text-center">{item.quantity}</td>
                                        <td className="text-right">{formatCurrency(item.price)}</td>
                                        <td className="text-right">{item.recurringPeriod || 'Monthly'}</td>
                                        <td className="text-right">{formatCurrency(item.price * item.quantity)}</td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}

                {/* Scope of Work */}
                {contractData.scope && (
                    <div className="contract-section">
                        <h3 
                            className="text-lg font-semibold mb-4"
                            style={{ color: style.accentColor }}
                        >
                            SCOPE OF WORK
                        </h3>
                        <div className="whitespace-pre-line">{contractData.scope}</div>
                    </div>
                )}

                {/* Payment Terms */}
                <div className="contract-section">
                    <h3 
                        className="text-lg font-semibold mb-4"
                        style={{ color: style.accentColor }}
                    >
                        PAYMENT TERMS
                    </h3>
                    <div className="whitespace-pre-line">
                        {contractData.paymentTerms || 'Payment terms have not been specified in this contract.'}
                    </div>
                </div>

                {/* Terms and Conditions */}
                <div className="contract-section">
                    <h3 
                        className="text-lg font-semibold mb-4"
                        style={{ color: style.accentColor }}
                    >
                        TERMS AND CONDITIONS
                    </h3>
                    <div className="whitespace-pre-line">{contractData.terms}</div>
                </div>

                {/* Renewal Terms */}
                {contractData.renewalTerms && (
                    <div className="contract-section">
                        <h3 
                            className="text-lg font-semibold mb-4"
                            style={{ color: style.accentColor }}
                        >
                            RENEWAL TERMS
                        </h3>
                        <div className="whitespace-pre-line">{contractData.renewalTerms}</div>
                    </div>
                )}

                {/* Signatures */}
                {templates.showSignature && (
                    <div className="signature-section">
                        <div className="signature-box">
                            <div className="signature-line">
                                {companyInfo.signature && (
                                    <img 
                                        src={companyInfo.signature} 
                                        alt="Service Provider Signature" 
                                        className="signature-image"
                                    />
                                )}
                            </div>
                            <p className="signature-name">{companyInfo.authorizedName || companyInfo.companyName}</p>
                            <p className="signature-title">Service Provider</p>
                            {contractData.signedAt1 && (
                                <p className="text-sm text-gray-600">Date: {formatDate(contractData.signedAt1)}</p>
                            )}
                        </div>

                        <div className="signature-box">
                            <div className="signature-line">
                                {contractData.customerSignature && (
                                    <img 
                                        src={contractData.customerSignature} 
                                        alt="Client Signature" 
                                        className="signature-image"
                                    />
                                )}
                                {isSigned && !contractData.customerSignature && (
                                    <img 
                                        src="https://upload.wikimedia.org/wikipedia/commons/a/ac/Green_tick.png" 
                                        alt="Accepted" 
                                        className="signature-image"
                                        style={{ maxHeight: '3rem' }}
                                    />
                                )}
                            </div>
                            <p className="signature-name">{customerData && customerData.name ? customerData.name : 'Client'}</p>
                            <p className="signature-title">Client</p>
                            {contractData.signedAt2 && (
                                <p className="text-sm text-gray-600">Date: {formatDate(contractData.signedAt2)}</p>
                            )}
                        </div>
                    </div>
                )}

                {/* Footer */}
                <div className="template-footer">
                    <p>This is a legally binding contract between the parties specified above.</p>
                    <p>Contract #{contractData.contractNumber || contract.objectId} • Page 1 of 1</p>
                    {companyInfo.companyWebsite && <p>{companyInfo.companyWebsite}</p>}
                </div>
            </div>
        );
    } catch (error) {
        console.error('ContractTemplate component error:', error);
        reportError(error);
        return (
            <div className="bg-red-50 border border-red-200 text-red-800 p-4 rounded">
                Error rendering contract template. Please try again.
            </div>
        );
    }
}
