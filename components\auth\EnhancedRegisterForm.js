function EnhancedRegisterForm({ onRegister, onSwitchToLogin }) {
    try {
        const [currentStep, setCurrentStep] = React.useState(1);
        const [formData, setFormData] = React.useState({
            // Personal Information
            name: '',
            email: '',
            password: '',
            confirmPassword: '',
            // Company Information
            companyName: '',
            companySize: '',
            industry: '',
            // Subscription
            selectedPlan: 'basic',
            acceptTerms: false
        });

        const [errors, setErrors] = React.useState({});
        const [loading, setLoading] = React.useState(false);
        const [passwordStrength, setPasswordStrength] = React.useState(null);

        const companySizes = [
            { value: '1-10', label: '1-10 employees' },
            { value: '11-50', label: '11-50 employees' },
            { value: '51-200', label: '51-200 employees' },
            { value: '201-1000', label: '201-1000 employees' },
            { value: '1000+', label: '1000+ employees' }
        ];

        const industries = [
            'Technology', 'Healthcare', 'Finance', 'Education', 'Retail',
            'Manufacturing', 'Real Estate', 'Consulting', 'Marketing', 'Other'
        ];

        const plans = [
            {
                id: 'basic',
                name: 'Basic',
                price: 999,
                features: ['Up to 100 leads', '3 team members', 'Basic reporting', 'Email support']
            },
            {
                id: 'professional',
                name: 'Professional',
                price: 2499,
                features: ['Up to 1,000 leads', '10 team members', 'Advanced reporting', 'Priority support'],
                popular: true
            },
            {
                id: 'enterprise',
                name: 'Enterprise',
                price: 4999,
                features: ['Unlimited leads', 'Unlimited team members', 'Custom reporting', '24/7 support']
            }
        ];

        const handleInputChange = (e) => {
            const { name, value, type, checked } = e.target;
            setFormData(prev => ({
                ...prev,
                [name]: type === 'checkbox' ? checked : value
            }));

            // Check password strength in real-time
            if (name === 'password' && window.SecurityUtils) {
                const strength = window.SecurityUtils.validatePasswordStrength(value);
                setPasswordStrength(strength);
            }
        };

        const nextStep = () => {
            if (validateCurrentStep()) {
                setCurrentStep(prev => prev + 1);
            }
        };

        const prevStep = () => {
            setCurrentStep(prev => prev - 1);
        };

        const validateCurrentStep = () => {
            const newErrors = {};
            
            if (currentStep === 1) {
                // Personal Information validation
                if (!formData.name) newErrors.name = 'Name is required';
                if (!formData.email) {
                    newErrors.email = 'Email is required';
                } else if (!isEmailValid(formData.email)) {
                    newErrors.email = 'Invalid email format';
                }
                if (!formData.password) {
                    newErrors.password = 'Password is required';
                } else if (passwordStrength && !passwordStrength.isValid) {
                    newErrors.password = 'Password must meet security requirements';
                }
                if (formData.password !== formData.confirmPassword) {
                    newErrors.confirmPassword = 'Passwords do not match';
                }
            } else if (currentStep === 2) {
                // Company Information validation
                if (!formData.companyName) newErrors.companyName = 'Company name is required';
                if (!formData.companySize) newErrors.companySize = 'Company size is required';
                if (!formData.industry) newErrors.industry = 'Industry is required';
            } else if (currentStep === 3) {
                // Terms validation
                if (!formData.acceptTerms) newErrors.acceptTerms = 'You must accept the terms and conditions';
            }

            setErrors(newErrors);
            return Object.keys(newErrors).length === 0;
        };

        const handleSubmit = async (e) => {
            e.preventDefault();
            if (!validateCurrentStep()) return;

            if (currentStep < 3) {
                nextStep();
                return;
            }

            setLoading(true);
            try {
                const response = await fetch('/api/register.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name: formData.name,
                        email: formData.email,
                        password: formData.password,
                        company_name: formData.companyName,
                        company_size: formData.companySize,
                        industry: formData.industry,
                        selected_plan: formData.selectedPlan
                    }),
                });

                const data = await response.json();
                if (data.success) {
                    if (window.toast) {
                        window.toast.success('Registration successful! Please check your email to verify your account.');
                    }
                    if (onRegister) onRegister(data);
                } else {
                    setErrors({ submit: data.message || 'Registration failed' });
                }
            } catch (error) {
                console.error('Registration error:', error);
                setErrors({ submit: 'Registration failed. Please try again.' });
            } finally {
                setLoading(false);
            }
        };

        const renderStepIndicator = () => (
            <div className="flex items-center justify-center mb-8">
                {[1, 2, 3].map((step) => (
                    <React.Fragment key={step}>
                        <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                            step <= currentStep ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'
                        }`}>
                            {step < currentStep ? (
                                <i className="fas fa-check text-sm"></i>
                            ) : (
                                <span className="text-sm font-medium">{step}</span>
                            )}
                        </div>
                        {step < 3 && (
                            <div className={`w-16 h-1 mx-2 ${
                                step < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                            }`}></div>
                        )}
                    </React.Fragment>
                ))}
            </div>
        );

        const renderStep1 = () => (
            <div className="space-y-6">
                <div className="text-center mb-6">
                    <h3 className="text-lg font-semibold text-gray-900">Personal Information</h3>
                    <p className="text-sm text-gray-600">Let's start with your basic details</p>
                </div>
                
                <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                        Full Name
                    </label>
                    <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                            errors.name ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Enter your full name"
                    />
                    {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
                </div>

                <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                        Email Address
                    </label>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                            errors.email ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Enter your email address"
                    />
                    {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email}</p>}
                </div>

                <div>
                    <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                        Password
                    </label>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        value={formData.password}
                        onChange={handleInputChange}
                        className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                            errors.password ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Create a strong password"
                    />
                    {passwordStrength && (
                        <div className="mt-2">
                            <div className="flex space-x-1">
                                {[1, 2, 3, 4].map((level) => (
                                    <div
                                        key={level}
                                        className={`h-1 flex-1 rounded ${
                                            level <= passwordStrength.score
                                                ? passwordStrength.score <= 2 ? 'bg-red-500' : 
                                                  passwordStrength.score === 3 ? 'bg-yellow-500' : 'bg-green-500'
                                                : 'bg-gray-200'
                                        }`}
                                    ></div>
                                ))}
                            </div>
                            <p className="text-xs text-gray-600 mt-1">{passwordStrength.feedback}</p>
                        </div>
                    )}
                    {errors.password && <p className="mt-1 text-sm text-red-600">{errors.password}</p>}
                </div>

                <div>
                    <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                        Confirm Password
                    </label>
                    <input
                        type="password"
                        id="confirmPassword"
                        name="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={handleInputChange}
                        className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                            errors.confirmPassword ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Confirm your password"
                    />
                    {errors.confirmPassword && <p className="mt-1 text-sm text-red-600">{errors.confirmPassword}</p>}
                </div>
            </div>
        );

        const renderStep2 = () => (
            <div className="space-y-6">
                <div className="text-center mb-6">
                    <h3 className="text-lg font-semibold text-gray-900">Company Information</h3>
                    <p className="text-sm text-gray-600">Tell us about your business</p>
                </div>
                
                <div>
                    <label htmlFor="companyName" className="block text-sm font-medium text-gray-700">
                        Company Name
                    </label>
                    <input
                        type="text"
                        id="companyName"
                        name="companyName"
                        value={formData.companyName}
                        onChange={handleInputChange}
                        className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                            errors.companyName ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Enter your company name"
                    />
                    {errors.companyName && <p className="mt-1 text-sm text-red-600">{errors.companyName}</p>}
                </div>

                <div>
                    <label htmlFor="companySize" className="block text-sm font-medium text-gray-700">
                        Company Size
                    </label>
                    <select
                        id="companySize"
                        name="companySize"
                        value={formData.companySize}
                        onChange={handleInputChange}
                        className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                            errors.companySize ? 'border-red-300' : 'border-gray-300'
                        }`}
                    >
                        <option value="">Select company size</option>
                        {companySizes.map((size) => (
                            <option key={size.value} value={size.value}>{size.label}</option>
                        ))}
                    </select>
                    {errors.companySize && <p className="mt-1 text-sm text-red-600">{errors.companySize}</p>}
                </div>

                <div>
                    <label htmlFor="industry" className="block text-sm font-medium text-gray-700">
                        Industry
                    </label>
                    <select
                        id="industry"
                        name="industry"
                        value={formData.industry}
                        onChange={handleInputChange}
                        className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                            errors.industry ? 'border-red-300' : 'border-gray-300'
                        }`}
                    >
                        <option value="">Select your industry</option>
                        {industries.map((industry) => (
                            <option key={industry} value={industry}>{industry}</option>
                        ))}
                    </select>
                    {errors.industry && <p className="mt-1 text-sm text-red-600">{errors.industry}</p>}
                </div>
            </div>
        );

        const renderStep3 = () => (
            <div className="space-y-6">
                <div className="text-center mb-6">
                    <h3 className="text-lg font-semibold text-gray-900">Choose Your Plan</h3>
                    <p className="text-sm text-gray-600">Select the plan that best fits your needs</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {plans.map((plan) => (
                        <div
                            key={plan.id}
                            className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                                formData.selectedPlan === plan.id
                                    ? 'border-blue-500 bg-blue-50'
                                    : 'border-gray-200 hover:border-gray-300'
                            } ${plan.popular ? 'ring-2 ring-blue-200' : ''}`}
                            onClick={() => setFormData(prev => ({ ...prev, selectedPlan: plan.id }))}
                        >
                            {plan.popular && (
                                <div className="text-center mb-2">
                                    <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                                        Most Popular
                                    </span>
                                </div>
                            )}
                            <div className="text-center">
                                <h4 className="text-lg font-semibold text-gray-900">{plan.name}</h4>
                                <div className="mt-2">
                                    <span className="text-2xl font-bold">₹{plan.price}</span>
                                    <span className="text-gray-500">/month</span>
                                </div>
                                <ul className="mt-4 space-y-2 text-sm text-gray-600">
                                    {plan.features.map((feature, index) => (
                                        <li key={index} className="flex items-center">
                                            <i className="fas fa-check text-green-500 mr-2"></i>
                                            {feature}
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        </div>
                    ))}
                </div>

                <div className="mt-6">
                    <label className="flex items-center">
                        <input
                            type="checkbox"
                            name="acceptTerms"
                            checked={formData.acceptTerms}
                            onChange={handleInputChange}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-600">
                            I agree to the{' '}
                            <a href="#" className="text-blue-600 hover:text-blue-500">Terms of Service</a>
                            {' '}and{' '}
                            <a href="#" className="text-blue-600 hover:text-blue-500">Privacy Policy</a>
                        </span>
                    </label>
                    {errors.acceptTerms && <p className="mt-1 text-sm text-red-600">{errors.acceptTerms}</p>}
                </div>
            </div>
        );

        return (
            <div className="max-w-2xl mx-auto p-6">
                {renderStepIndicator()}

                <form onSubmit={handleSubmit}>
                    {currentStep === 1 && renderStep1()}
                    {currentStep === 2 && renderStep2()}
                    {currentStep === 3 && renderStep3()}
                    
                    {errors.submit && (
                        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                            <p className="text-sm text-red-600">{errors.submit}</p>
                        </div>
                    )}

                    <div className="flex justify-between mt-8">
                        {currentStep > 1 && (
                            <button
                                type="button"
                                onClick={prevStep}
                                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                            >
                                Previous
                            </button>
                        )}
                        
                        <div className="ml-auto">
                            <button
                                type="submit"
                                disabled={loading}
                                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
                            >
                                {loading ? (
                                    <span className="flex items-center">
                                        <i className="fas fa-spinner fa-spin mr-2"></i>
                                        Processing...
                                    </span>
                                ) : currentStep < 3 ? 'Next' : 'Create Account'}
                            </button>
                        </div>
                    </div>
                </form>

                {onSwitchToLogin && (
                    <div className="text-center mt-6">
                        <p className="text-sm text-gray-600">
                            Already have an account?{' '}
                            <button
                                onClick={onSwitchToLogin}
                                className="font-medium text-blue-600 hover:text-blue-500"
                            >
                                Sign in
                            </button>
                        </p>
                    </div>
                )}
            </div>
        );
    } catch (error) {
        console.error('Enhanced register form error:', error);
        reportError(error);
        return null;
    }
}
