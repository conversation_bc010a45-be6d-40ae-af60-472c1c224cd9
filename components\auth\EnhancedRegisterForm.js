function EnhancedRegisterForm({ onRegister, onSwitchToLogin }) {
    try {
        const [currentStep, setCurrentStep] = React.useState(1);
        const [formData, setFormData] = React.useState({
            // Personal Information
            name: '',
            email: '',
            password: '',
            confirmPassword: '',
            // Company Information
            companyName: '',
            companySize: '',
            industry: '',
            // Subscription
            selectedPlan: 'basic',
            acceptTerms: false
        });

        const [errors, setErrors] = React.useState({});
        const [loading, setLoading] = React.useState(false);
        const [passwordStrength, setPasswordStrength] = React.useState(null);

        const companySizes = [
            { value: '1-10', label: '1-10 employees' },
            { value: '11-50', label: '11-50 employees' },
            { value: '51-200', label: '51-200 employees' },
            { value: '201-1000', label: '201-1000 employees' },
            { value: '1000+', label: '1000+ employees' }
        ];

        const industries = [
            'Technology', 'Healthcare', 'Finance', 'Education', 'Retail',
            'Manufacturing', 'Real Estate', 'Consulting', 'Marketing', 'Other'
        ];

        const [billingCycle, setBillingCycle] = React.useState('monthly');

        const planDetails = {
            monthly: { price: 500, period: 'month', savings: null },
            yearly: { price: 5000, period: 'year', savings: 1000 }
        };

        const features = [
            'Customer Management',
            'Invoice Generation',
            'Quotation Management',
            'Contract Management',
            'Lead Tracking',
            'Business Analytics',
            'Email Notifications',
            'Data Export',
            'Multi-user Access',
            '24/7 Support'
        ];

        const handleInputChange = (e) => {
            const { name, value, type, checked } = e.target;
            setFormData(prev => ({
                ...prev,
                [name]: type === 'checkbox' ? checked : value
            }));

            // Check password strength in real-time
            if (name === 'password' && window.SecurityUtils) {
                const strength = window.SecurityUtils.validatePasswordStrength(value);
                setPasswordStrength(strength);
            }
        };

        const nextStep = () => {
            if (validateCurrentStep()) {
                setCurrentStep(prev => prev + 1);
            }
        };

        const prevStep = () => {
            setCurrentStep(prev => prev - 1);
        };

        const validateCurrentStep = () => {
            const newErrors = {};
            
            if (currentStep === 1) {
                // Personal Information validation
                if (!formData.name) newErrors.name = 'Name is required';
                if (!formData.email) {
                    newErrors.email = 'Email is required';
                } else if (!isEmailValid(formData.email)) {
                    newErrors.email = 'Invalid email format';
                }
                if (!formData.password) {
                    newErrors.password = 'Password is required';
                } else if (passwordStrength && !passwordStrength.isValid) {
                    newErrors.password = 'Password must meet security requirements';
                }
                if (formData.password !== formData.confirmPassword) {
                    newErrors.confirmPassword = 'Passwords do not match';
                }
            } else if (currentStep === 2) {
                // Company Information validation
                if (!formData.companyName) newErrors.companyName = 'Company name is required';
                if (!formData.companySize) newErrors.companySize = 'Company size is required';
                if (!formData.industry) newErrors.industry = 'Industry is required';
            } else if (currentStep === 3) {
                // Terms validation
                if (!formData.acceptTerms) newErrors.acceptTerms = 'You must accept the terms and conditions';
            }

            setErrors(newErrors);
            return Object.keys(newErrors).length === 0;
        };

        const handleSubmit = async (e) => {
            e.preventDefault();
            if (!validateCurrentStep()) return;

            if (currentStep < 3) {
                nextStep();
                return;
            }

            setLoading(true);
            try {
                const apiUrl = window.getApiUrl ? window.getApiUrl('/register') : '/api/register.php';
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name: formData.name,
                        email: formData.email,
                        password: formData.password,
                        company_name: formData.companyName,
                        company_size: formData.companySize,
                        industry: formData.industry,
                        selected_plan: 'basic',
                        billing_cycle: billingCycle,
                        plan_price: planDetails[billingCycle].price
                    }),
                });

                const data = await response.json();
                if (data.success) {
                    if (window.toast) {
                        window.toast.success('Registration successful! Please check your email to verify your account.');
                    }
                    if (onRegister) onRegister(data);
                } else {
                    setErrors({ submit: data.message || 'Registration failed' });
                }
            } catch (error) {
                console.error('Registration error:', error);
                setErrors({ submit: 'Registration failed. Please try again.' });
            } finally {
                setLoading(false);
            }
        };

        const renderStepIndicator = () => (
            <div className="flex items-center justify-center mb-8">
                {[1, 2, 3].map((step) => (
                    <React.Fragment key={step}>
                        <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                            step <= currentStep ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'
                        }`}>
                            {step < currentStep ? (
                                <i className="fas fa-check text-sm"></i>
                            ) : (
                                <span className="text-sm font-medium">{step}</span>
                            )}
                        </div>
                        {step < 3 && (
                            <div className={`w-16 h-1 mx-2 ${
                                step < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                            }`}></div>
                        )}
                    </React.Fragment>
                ))}
            </div>
        );

        const renderStep1 = () => (
            <div className="space-y-6">
                <div className="text-center mb-6">
                    <h3 className="text-lg font-semibold text-gray-900">Personal Information</h3>
                    <p className="text-sm text-gray-600">Let's start with your basic details</p>
                </div>
                
                <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                        Full Name
                    </label>
                    <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                            errors.name ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Enter your full name"
                    />
                    {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
                </div>

                <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                        Email Address
                    </label>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                            errors.email ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Enter your email address"
                    />
                    {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email}</p>}
                </div>

                <div>
                    <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                        Password
                    </label>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        value={formData.password}
                        onChange={handleInputChange}
                        className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                            errors.password ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Create a strong password"
                    />
                    {passwordStrength && (
                        <div className="mt-2">
                            <div className="flex space-x-1">
                                {[1, 2, 3, 4].map((level) => (
                                    <div
                                        key={level}
                                        className={`h-1 flex-1 rounded ${
                                            level <= passwordStrength.score
                                                ? passwordStrength.score <= 2 ? 'bg-red-500' : 
                                                  passwordStrength.score === 3 ? 'bg-yellow-500' : 'bg-green-500'
                                                : 'bg-gray-200'
                                        }`}
                                    ></div>
                                ))}
                            </div>
                            <p className="text-xs text-gray-600 mt-1">{passwordStrength.feedback}</p>
                        </div>
                    )}
                    {errors.password && <p className="mt-1 text-sm text-red-600">{errors.password}</p>}
                </div>

                <div>
                    <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                        Confirm Password
                    </label>
                    <input
                        type="password"
                        id="confirmPassword"
                        name="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={handleInputChange}
                        className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                            errors.confirmPassword ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Confirm your password"
                    />
                    {errors.confirmPassword && <p className="mt-1 text-sm text-red-600">{errors.confirmPassword}</p>}
                </div>
            </div>
        );

        const renderStep2 = () => (
            <div className="space-y-6">
                <div className="text-center mb-6">
                    <h3 className="text-lg font-semibold text-gray-900">Company Information</h3>
                    <p className="text-sm text-gray-600">Tell us about your business</p>
                </div>
                
                <div>
                    <label htmlFor="companyName" className="block text-sm font-medium text-gray-700">
                        Company Name
                    </label>
                    <input
                        type="text"
                        id="companyName"
                        name="companyName"
                        value={formData.companyName}
                        onChange={handleInputChange}
                        className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                            errors.companyName ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Enter your company name"
                    />
                    {errors.companyName && <p className="mt-1 text-sm text-red-600">{errors.companyName}</p>}
                </div>

                <div>
                    <label htmlFor="companySize" className="block text-sm font-medium text-gray-700">
                        Company Size
                    </label>
                    <select
                        id="companySize"
                        name="companySize"
                        value={formData.companySize}
                        onChange={handleInputChange}
                        className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                            errors.companySize ? 'border-red-300' : 'border-gray-300'
                        }`}
                    >
                        <option value="">Select company size</option>
                        {companySizes.map((size) => (
                            <option key={size.value} value={size.value}>{size.label}</option>
                        ))}
                    </select>
                    {errors.companySize && <p className="mt-1 text-sm text-red-600">{errors.companySize}</p>}
                </div>

                <div>
                    <label htmlFor="industry" className="block text-sm font-medium text-gray-700">
                        Industry
                    </label>
                    <select
                        id="industry"
                        name="industry"
                        value={formData.industry}
                        onChange={handleInputChange}
                        className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                            errors.industry ? 'border-red-300' : 'border-gray-300'
                        }`}
                    >
                        <option value="">Select your industry</option>
                        {industries.map((industry) => (
                            <option key={industry} value={industry}>{industry}</option>
                        ))}
                    </select>
                    {errors.industry && <p className="mt-1 text-sm text-red-600">{errors.industry}</p>}
                </div>
            </div>
        );

        const renderStep3 = () => (
            <div className="space-y-6">
                <div className="text-center mb-6">
                    <h3 className="text-lg font-semibold text-gray-900">Choose Your Plan</h3>
                    <p className="text-sm text-gray-600">Simple, transparent pricing for your business</p>
                </div>

                {/* Billing Toggle */}
                <div className="flex justify-center mb-6">
                    <div className="bg-gray-100 p-1 rounded-lg flex">
                        <button
                            type="button"
                            onClick={() => setBillingCycle('monthly')}
                            className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                                billingCycle === 'monthly'
                                    ? 'bg-white text-blue-600 shadow-sm'
                                    : 'text-gray-600 hover:text-gray-900'
                            }`}
                        >
                            Monthly
                        </button>
                        <button
                            type="button"
                            onClick={() => setBillingCycle('yearly')}
                            className={`px-4 py-2 rounded-md text-sm font-medium transition-all relative ${
                                billingCycle === 'yearly'
                                    ? 'bg-white text-blue-600 shadow-sm'
                                    : 'text-gray-600 hover:text-gray-900'
                            }`}
                        >
                            Yearly
                            <span className="absolute -top-1 -right-1 bg-green-500 text-white text-xs px-1 py-0.5 rounded-full">
                                Save ₹1K
                            </span>
                        </button>
                    </div>
                </div>

                {/* Single Plan Card */}
                <div className="max-w-md mx-auto">
                    <div className="bg-white rounded-xl shadow-lg border-2 border-blue-500 relative overflow-hidden">
                        {/* Popular Badge */}
                        <div className="bg-blue-500 text-white text-center py-2 px-4">
                            <span className="text-sm font-semibold">Complete Business Solution</span>
                        </div>

                        <div className="p-6">
                            {/* Price */}
                            <div className="text-center mb-6">
                                <div className="flex items-baseline justify-center">
                                    <span className="text-3xl font-bold text-gray-900">
                                        ₹{planDetails[billingCycle].price.toLocaleString()}
                                    </span>
                                    <span className="text-lg text-gray-600 ml-2">
                                        /{planDetails[billingCycle].period}
                                    </span>
                                </div>
                                {billingCycle === 'yearly' && (
                                    <div className="mt-1">
                                        <span className="text-sm text-green-600 font-medium">
                                            Save ₹1,000 compared to monthly
                                        </span>
                                    </div>
                                )}
                            </div>

                            {/* Features */}
                            <div className="mb-6">
                                <h4 className="text-sm font-semibold text-gray-900 mb-3">
                                    Everything included:
                                </h4>
                                <ul className="space-y-2">
                                    {features.slice(0, 6).map((feature, index) => (
                                        <li key={index} className="flex items-center text-sm">
                                            <svg className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                            </svg>
                                            <span className="text-gray-700">{feature}</span>
                                        </li>
                                    ))}
                                    <li className="text-sm text-gray-500 ml-6">+ 4 more features</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="mt-6">
                    <label className="flex items-center">
                        <input
                            type="checkbox"
                            name="acceptTerms"
                            checked={formData.acceptTerms}
                            onChange={handleInputChange}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-600">
                            I agree to the{' '}
                            <a href="/terms" className="text-blue-600 hover:text-blue-500">Terms of Service</a>
                            {' '}and{' '}
                            <a href="/privacy" className="text-blue-600 hover:text-blue-500">Privacy Policy</a>
                        </span>
                    </label>
                    {errors.acceptTerms && <p className="mt-1 text-sm text-red-600">{errors.acceptTerms}</p>}
                </div>
            </div>
        );

        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
                <div className="max-w-2xl mx-auto">
                    {/* Header */}
                    <div className="text-center mb-8">
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">
                            Start Your Business Journey
                        </h1>
                        <p className="text-lg text-gray-600">
                            Join thousands of businesses managing their operations with our platform
                        </p>
                    </div>

                    {/* Form Container */}
                    <div className="bg-white rounded-2xl shadow-xl p-8">
                        {renderStepIndicator()}

                        <form onSubmit={handleSubmit} className="mt-8">
                            {currentStep === 1 && renderStep1()}
                            {currentStep === 2 && renderStep2()}
                            {currentStep === 3 && renderStep3()}

                            {errors.submit && (
                                <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                                    <div className="flex">
                                        <svg className="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                        </svg>
                                        <p className="text-sm text-red-600">{errors.submit}</p>
                                    </div>
                                </div>
                            )}

                            <div className="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
                                {currentStep > 1 ? (
                                    <button
                                        type="button"
                                        onClick={prevStep}
                                        className="flex items-center px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                                    >
                                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                        </svg>
                                        Previous
                                    </button>
                                ) : (
                                    <div></div>
                                )}

                                <button
                                    type="submit"
                                    disabled={loading}
                                    className="flex items-center px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-semibold"
                                >
                                    {loading ? (
                                        <>
                                            <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            Processing...
                                        </>
                                    ) : (
                                        <>
                                            {currentStep === 3 ? 'Create Account' : 'Continue'}
                                            {currentStep < 3 && (
                                                <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                                </svg>
                                            )}
                                        </>
                                    )}
                                </button>
                            </div>
                        </form>
                    </div>

                    {/* Footer */}
                    <div className="text-center mt-8">
                        <p className="text-sm text-gray-600">
                            Already have an account?{' '}
                            {onSwitchToLogin ? (
                                <button
                                    onClick={onSwitchToLogin}
                                    className="text-blue-600 hover:text-blue-500 font-medium"
                                >
                                    Sign in here
                                </button>
                            ) : (
                                <a href="/login" className="text-blue-600 hover:text-blue-500 font-medium">
                                    Sign in here
                                </a>
                            )}
                        </p>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('Enhanced register form error:', error);
        reportError(error);
        return null;
    }
}

// Make EnhancedRegisterForm component globally available
window.EnhancedRegisterForm = EnhancedRegisterForm;
