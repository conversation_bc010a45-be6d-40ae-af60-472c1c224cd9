<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Registration API</title>
</head>
<body>
    <h1>Test Registration API</h1>
    <button onclick="testRegistration()">Test Registration</button>
    <div id="result"></div>

    <script>
        async function testRegistration() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('/biz/api/register.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name: 'Test User',
                        email: '<EMAIL>',
                        password: 'TestPassword123!',
                        company_name: 'Test Company',
                        company_size: '1-10',
                        industry: 'Technology',
                        selected_plan: 'basic',
                        billing_cycle: 'monthly'
                    })
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                const contentType = response.headers.get('content-type');
                console.log('Content-Type:', contentType);
                
                if (!contentType || !contentType.includes('application/json')) {
                    const textResponse = await response.text();
                    console.error('Non-JSON response:', textResponse);
                    resultDiv.innerHTML = `<h3>Error: Non-JSON Response</h3><pre>${textResponse}</pre>`;
                    return;
                }

                const data = await response.json();
                console.log('Response data:', data);
                
                resultDiv.innerHTML = `<h3>Response:</h3><pre>${JSON.stringify(data, null, 2)}</pre>`;
                
            } catch (error) {
                console.error('Test error:', error);
                resultDiv.innerHTML = `<h3>Error:</h3><pre>${error.message}</pre>`;
            }
        }
    </script>
</body>
</html>
