'use strict';

// Import React explicitly for createContext
const React = window.React;
const ReactDOM = window.ReactDOM;

// Error Boundary Component
class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error) {
        return { hasError: true };
    }

    componentDidCatch(error, errorInfo) {
        console.error('React Error Boundary caught an error:', error, errorInfo);
    }

    render() {
        if (this.state.hasError) {
            return (
                <div className="p-8 text-red-600">
                    <h1>Something went wrong.</h1>
                    <p>Please try refreshing the page.</p>
                </div>
            );
        }

        return this.props.children;
    }
}

// AuthContext will be created by AuthContext.js component
// We'll use the global window.AuthContext that gets set by that component

function App() {
    try {
        const [currentPage, setCurrentPage] = React.useState('landing');
        const [isLoading, setIsLoading] = React.useState(true);
        
        // Use the AuthContext for authentication state
        const auth = React.useContext(window.AuthContext);
        const { 
            isAuthenticated, 
            user, 
            token, 
            loading: authLoading, 
            login, 
            logout 
        } = auth || {};
        
        // Update loading state when auth state changes
        React.useEffect(() => {
            if (!authLoading) {
                setIsLoading(false);
            }
        }, [authLoading]);
        
        // Set initial page based on authentication status
        React.useEffect(() => {
            if (!authLoading) {
                if (isAuthenticated) {
                    // If authenticated and on a public page, redirect to dashboard
                    const publicPages = ['landing', 'login', 'register', 'forgot-password', 'reset-password'];
                    if (publicPages.includes(currentPage)) {
                        setCurrentPage('dashboard');
                    }
                } else {
                    // If not authenticated and on a protected page, redirect to landing
                    const publicPages = ['landing', 'login', 'register', 'forgot-password', 'reset-password'];
                    if (!publicPages.includes(currentPage)) {
                        setCurrentPage('landing');
                    }
                }
            }
        }, [isAuthenticated, authLoading]);
        
        // Handle login success
        const handleLogin = (userData, authToken) => {
            // Redirect to dashboard after login
            const dashboardUrl = window.getAppUrl ? window.getAppUrl('/dashboard') : '/dashboard';
            window.history.pushState({}, '', dashboardUrl);
            setCurrentPage('dashboard');
        };

        // Handle logout
        const handleLogout = () => {
            logout();
        };

        const renderAuthPage = () => {
            switch (currentPage) {
                case 'login':
                    return <LoginForm onLogin={handleLogin} />;
                case 'register':
                    return <RegisterForm onRegister={handleLogin} />;
                case 'forgot-password':
                    return <ForgotPasswordForm />;
                case 'reset-password':
                    return <ResetPasswordForm />;
                default:
                    return <LoginForm onLogin={handleLogin} />;
            }
        };

        const renderPage = () => {
            console.log('App - Rendering page:', currentPage);
            switch (currentPage) {
                case 'landing':
                    return <LandingPage />;
                case 'dashboard':
                    return <Dashboard />;
                case 'super-admin':
                    console.log('App - Rendering SuperAdminDashboard');
                    return <SuperAdminDashboard />;
                case 'customers':
                    return <Customers />;
                case 'leads':
                    return <Leads />;
                case 'quotations':
                    return <Quotations />;
                case 'invoices':
                    return <Invoices />;
                case 'contracts':
                    return <Contracts />;
                case 'items':
                    return <Items />;
                case 'reports':
                    return <Reports />;
                case 'subscriptions':
                    return <Subscriptions />;
                case 'payment':
                    // Check for payment success page
                    const subPath = window.location.pathname.split('/')[2];
                    if (subPath === 'success') {
                        return <PaymentSuccess />;
                    }
                    return <Dashboard />;
                case 'settings':
                    return <Settings />;
                case 'profile':
                    return <ProfilePage user={user} />;
                default:
                    return <Dashboard />;
            }
        };

        // Enhanced route handling with better path parsing and state management
        React.useEffect(() => {
            const handleRouteChange = () => {
                console.log('App - handleRouteChange called');
                const path = window.location.pathname;
                const basePath = window.APP_CONFIG && window.APP_CONFIG.BASE_PATH ? window.APP_CONFIG.BASE_PATH : '';
                const queryParams = new URLSearchParams(window.location.search);
                
                console.log('App - Current path:', path);
                console.log('App - Base path:', basePath);

                // Remove base path from the current path to get the app route
                let appPath = path;
                if (basePath && path.startsWith(basePath)) {
                    appPath = path.substring(basePath.length);
                }
                console.log('App - App path after base path removal:', appPath);

                // Default page based on authentication status
                let page = appPath.substring(1) || (isAuthenticated ? 'dashboard' : 'landing');
                console.log('App - Initial page determination:', page);

                // Handle sub-paths and query parameters
                const pathParts = appPath.split('/').filter(part => part);
                console.log('App - Path parts:', pathParts);
                
                if (pathParts.length > 0) {
                    page = pathParts[0];
                    console.log('App - Page from path parts:', page);
                    
                    // Store sub-path information for deeper routing
                    if (pathParts.length > 1) {
                        window.APP_ROUTE_PARAMS = {
                            id: pathParts[1],
                            action: pathParts[2] || null,
                            params: Object.fromEntries(queryParams.entries())
                        };
                    } else {
                        window.APP_ROUTE_PARAMS = {
                            id: null,
                            action: null,
                            params: Object.fromEntries(queryParams.entries())
                        };
                    }
                }

                // Define public and protected routes
                const publicRoutes = ['landing', 'login', 'register', 'forgot-password', 'reset-password', 'payment'];
                const protectedRoutes = [
                    'dashboard', 'customers', 'leads', 'quotations', 'invoices', 
                    'contracts', 'items', 'reports', 'subscriptions', 'settings', 
                    'profile', 'super-admin'
                ];
                
                // Route protection logic
                if (!isAuthenticated && protectedRoutes.includes(page)) {
                    console.log('Protected route accessed without authentication, redirecting to landing');
                    page = 'landing';
                    const landingUrl = window.getAppUrl ? window.getAppUrl('/') : '/';
                    window.history.replaceState({}, '', landingUrl);
                }
                
                // Special case for payment success page which needs token but has a different flow
                if (page === 'payment' && pathParts[1] === 'success') {
                    // Allow payment success page even if not fully authenticated
                    page = 'payment';
                }
                
                // Special handling for super-admin page
                if (page === 'super-admin') {
                    console.log('App - Super admin page detected');
                    // Check if user has super_admin role
                    const currentUser = auth && auth.user;
                    console.log('App - Current user:', currentUser);
                    console.log('App - User role:', currentUser && currentUser.role);
                    
                    if (currentUser && currentUser.role === 'super_admin') {
                        console.log('App - User is a super admin, allowing access');
                    } else {
                        console.log('App - User is not a super admin, redirecting to dashboard');
                        page = 'dashboard';
                        const dashboardUrl = window.getAppUrl ? window.getAppUrl('/dashboard') : '/dashboard';
                        window.history.replaceState({}, '', dashboardUrl);
                    }
                }

                // Update current page state
                console.log('App - Setting current page to:', page);
                setCurrentPage(page);
                
                // Log navigation for debugging
                console.log(`Navigation: ${page}${(window.APP_ROUTE_PARAMS && window.APP_ROUTE_PARAMS.id) ? '/' + window.APP_ROUTE_PARAMS.id : ''}`);
            };

            // Listen for route changes
            window.addEventListener('popstate', handleRouteChange);
            handleRouteChange();

            // Custom event for programmatic navigation
            const handleCustomNavigation = (event) => {
                console.log('App - Navigation event received:', event);
                console.log('App - Navigation event detail:', event && event.detail);
                
                if (event && event.detail) {
                    const { page, id, action, params } = event.detail;
                    console.log('App - Navigating to page:', page);
                    
                    // Build the URL
                    let url = `/${page}`;
                    if (id) url += `/${id}`;
                    if (action) url += `/${action}`;
                    
                    // Add query parameters if provided
                    if (params && Object.keys(params).length > 0) {
                        const queryString = new URLSearchParams(params).toString();
                        url += `?${queryString}`;
                    }
                    
                    console.log('App - Built URL:', url);
                    
                    // Update history and trigger route change
                    const fullUrl = window.getAppUrl ? window.getAppUrl(url) : url;
                    console.log('App - Full URL:', fullUrl);
                    
                    window.history.pushState({}, '', fullUrl);
                    console.log('App - History state updated');
                    
                    // Set the current page directly
                    setCurrentPage(page);
                    console.log('App - Current page set to:', page);
                    
                    // Also trigger the route change handler
                    handleRouteChange();
                    console.log('App - Route change handled');
                }
            };
            
            window.addEventListener('app-navigate', handleCustomNavigation);

            return () => {
                window.removeEventListener('popstate', handleRouteChange);
                window.removeEventListener('app-navigate', handleCustomNavigation);
            };
        }, [isAuthenticated]);

        // Show loading spinner while checking authentication
        if (isLoading || authLoading) {
            return (
                <div className="min-h-screen flex items-center justify-center bg-gray-50">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                        <p className="mt-4 text-gray-600">Loading application...</p>
                    </div>
                </div>
            );
        }

        // Show landing page or authentication pages if not authenticated
        if (!isAuthenticated) {
            if (currentPage === 'landing') {
                return (
                    <div className="app">
                        <LandingPage />
                        <ToastManager />
                    </div>
                );
            } else {
                return (
                    <div className="app">
                        {renderAuthPage()}
                        <ToastManager />
                    </div>
                );
            }
        }

        // Show main application if authenticated
        return (
            <div className="app">
                <MainLayout
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                    user={user}
                    onLogout={handleLogout}
                >
                    {renderPage()}
                </MainLayout>
                <ToastManager />
            </div>
        );
    } catch (error) {
        console.error('App component error:', error);
        reportError(error);
        return <div className="p-8 text-red-600">An error occurred loading the application. Please try refreshing the page.</div>;
    }
}

// Initialize React 18 with createRoot
// Check if DOM is already loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
} else {
    // DOM is already loaded
    initializeApp();
}

function initializeApp() {
    try {
        const rootElement = document.getElementById('root');
        if (!rootElement) {
            console.error('Root element not found!');
            return;
        }

        // Create the AuthProvider component
        const AuthProvider = window.AuthProvider || function({ children }) {
            return children;
        };

        const root = ReactDOM.createRoot(rootElement);
        root.render(
            React.createElement(React.StrictMode, null,
                React.createElement(ErrorBoundary, null,
                    React.createElement(AuthProvider, null,
                        React.createElement(App, null)
                    )
                )
            )
        );
        
        // Log successful initialization
        console.log('Application initialized successfully');
        
        // Performance mark for measuring load time
        if (window.performance && window.performance.mark) {
            window.performance.mark('app-loaded');
            window.performance.measure('app-load-time', 'app-start', 'app-loaded');
            const loadTime = window.performance.getEntriesByName('app-load-time')[0];
            console.log(`App loaded in ${loadTime.duration.toFixed(2)}ms`);
        }
    } catch (error) {
        console.error('Error initializing app:', error);
        reportError(error);
    }
}