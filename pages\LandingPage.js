// Make LandingPage component globally available
window.LandingPage = function LandingPage() {
    try {
        const [showLoginModal, setShowLoginModal] = React.useState(false);
        const [showRegisterModal, setShowRegisterModal] = React.useState(false);
        const [showForgotPasswordModal, setShowForgotPasswordModal] = React.useState(false);

        const features = [
            {
                icon: 'fas fa-users',
                title: 'Customer Management',
                description: 'Organize and manage your customer relationships with powerful CRM tools.'
            },
            {
                icon: 'fas fa-file-invoice-dollar',
                title: 'Smart Invoicing',
                description: 'Create professional invoices and quotations with automated calculations.'
            },
            {
                icon: 'fas fa-chart-line',
                title: 'Business Analytics',
                description: 'Get insights into your business performance with detailed reports and analytics.'
            },
            {
                icon: 'fas fa-mobile-alt',
                title: 'Mobile Ready',
                description: 'Access your business data anywhere, anytime with our responsive design.'
            },
            {
                icon: 'fas fa-shield-alt',
                title: 'Secure & Reliable',
                description: 'Enterprise-grade security with 99.9% uptime guarantee for your peace of mind.'
            },
            {
                icon: 'fas fa-headset',
                title: '24/7 Support',
                description: 'Get help when you need it with our dedicated customer support team.'
            }
        ];



        const handleGetStarted = () => {
            setShowRegisterModal(true);
        };

        const handleLogin = () => {
            setShowLoginModal(true);
        };

        const scrollToSection = (sectionId) => {
            const element = document.getElementById(sectionId);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
            }
        };

        return (
            <div className="min-h-screen bg-white">
                {/* Navigation */}
                <nav className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="flex justify-between items-center h-16">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <h1 className="text-2xl font-bold text-blue-600">Bizma</h1>
                                </div>
                                <div className="hidden md:block ml-10">
                                    <div className="flex items-baseline space-x-8">
                                        <button 
                                            onClick={() => scrollToSection('features')}
                                            className="text-gray-600 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors"
                                        >
                                            Features
                                        </button>
                                        <button 
                                            onClick={() => scrollToSection('pricing')}
                                            className="text-gray-600 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors"
                                        >
                                            Pricing
                                        </button>
                                        <button 
                                            onClick={() => scrollToSection('about')}
                                            className="text-gray-600 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors"
                                        >
                                            About
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div className="flex items-center space-x-4">
                                <button
                                    onClick={handleLogin}
                                    className="text-gray-600 hover:text-blue-600 px-4 py-2 text-sm font-medium transition-colors"
                                >
                                    Sign In
                                </button>
                                <button
                                    onClick={handleGetStarted}
                                    className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg text-sm font-medium transition-colors"
                                >
                                    Get Started
                                </button>
                            </div>
                        </div>
                    </div>
                </nav>

                {/* Hero Section */}
                <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                                Grow Your Business with
                                <span className="text-blue-600"> Bizma</span>
                            </h1>
                            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                                The all-in-one business management platform that helps you manage customers, 
                                create invoices, track leads, and grow your business efficiently.
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <button
                                    onClick={handleGetStarted}
                                    className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors shadow-lg"
                                >
                                    Start Free Trial
                                </button>
                                <button
                                    onClick={() => scrollToSection('features')}
                                    className="border border-gray-300 hover:border-blue-600 text-gray-700 hover:text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold transition-colors"
                                >
                                    Learn More
                                </button>
                            </div>
                            <p className="text-sm text-gray-500 mt-4">
                                No credit card required • 14-day free trial • Cancel anytime
                            </p>
                        </div>
                    </div>
                </section>

                {/* Features Section */}
                <section id="features" className="py-20 bg-white">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                                Everything you need to run your business
                            </h2>
                            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                                Bizma provides all the tools you need to manage your business operations 
                                efficiently and scale your growth.
                            </p>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                            {features.map((feature, index) => (
                                <div key={index} className="text-center p-6 rounded-lg hover:shadow-lg transition-shadow">
                                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <i className={`${feature.icon} text-2xl text-blue-600`}></i>
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
                                    <p className="text-gray-600">{feature.description}</p>
                                </div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Pricing Section */}
                <section id="pricing" className="py-20 bg-gray-50">
                    <SimplePricingPlans
                        onPlanSelect={(planData) => {
                            console.log('Plan selected:', planData);
                            handleGetStarted();
                        }}
                        showTitle={true}
                    />
                </section>

                {/* About Section */}
                <section id="about" className="py-20 bg-white">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                                Why choose Bizma?
                            </h2>
                            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                                Built by entrepreneurs, for entrepreneurs. We understand the challenges 
                                of running a business and have created the perfect solution.
                            </p>
                        </div>
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                            <div>
                                <h3 className="text-2xl font-bold text-gray-900 mb-6">
                                    Trusted by thousands of businesses
                                </h3>
                                <div className="space-y-4">
                                    <div className="flex items-center">
                                        <i className="fas fa-check-circle text-green-500 mr-3"></i>
                                        <span className="text-gray-700">99.9% uptime guarantee</span>
                                    </div>
                                    <div className="flex items-center">
                                        <i className="fas fa-check-circle text-green-500 mr-3"></i>
                                        <span className="text-gray-700">Bank-level security</span>
                                    </div>
                                    <div className="flex items-center">
                                        <i className="fas fa-check-circle text-green-500 mr-3"></i>
                                        <span className="text-gray-700">24/7 customer support</span>
                                    </div>
                                    <div className="flex items-center">
                                        <i className="fas fa-check-circle text-green-500 mr-3"></i>
                                        <span className="text-gray-700">Regular feature updates</span>
                                    </div>
                                </div>
                            </div>
                            <div className="text-center">
                                <div className="bg-blue-50 rounded-lg p-8">
                                    <i className="fas fa-rocket text-4xl text-blue-600 mb-4"></i>
                                    <h4 className="text-xl font-semibold text-gray-900 mb-2">
                                        Ready to get started?
                                    </h4>
                                    <p className="text-gray-600 mb-6">
                                        Join thousands of businesses already using Bizma to grow their operations.
                                    </p>
                                    <button
                                        onClick={handleGetStarted}
                                        className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
                                    >
                                        Start Your Free Trial
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Footer */}
                <footer className="bg-gray-900 text-white py-16">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
                            <div className="md:col-span-2">
                                <h3 className="text-2xl font-bold mb-4">Bizma</h3>
                                <p className="text-gray-400 mb-6 max-w-md">
                                    The all-in-one business management platform designed for modern entrepreneurs.
                                    Streamline your operations, manage customers, and grow your business with confidence.
                                </p>
                                <div className="flex space-x-4">
                                    <a href="#" className="text-gray-400 hover:text-white transition-colors">
                                        <i className="fab fa-facebook-f text-xl"></i>
                                    </a>
                                    <a href="#" className="text-gray-400 hover:text-white transition-colors">
                                        <i className="fab fa-twitter text-xl"></i>
                                    </a>
                                    <a href="#" className="text-gray-400 hover:text-white transition-colors">
                                        <i className="fab fa-linkedin-in text-xl"></i>
                                    </a>
                                    <a href="#" className="text-gray-400 hover:text-white transition-colors">
                                        <i className="fab fa-instagram text-xl"></i>
                                    </a>
                                </div>
                            </div>
                            <div>
                                <h4 className="text-lg font-semibold mb-4">Product</h4>
                                <ul className="space-y-3 text-gray-400">
                                    <li>
                                        <button
                                            onClick={() => scrollToSection('features')}
                                            className="hover:text-white transition-colors"
                                        >
                                            Features
                                        </button>
                                    </li>
                                    <li>
                                        <button
                                            onClick={() => scrollToSection('pricing')}
                                            className="hover:text-white transition-colors"
                                        >
                                            Pricing
                                        </button>
                                    </li>
                                    <li>
                                        <button
                                            onClick={() => scrollToSection('about')}
                                            className="hover:text-white transition-colors"
                                        >
                                            About Us
                                        </button>
                                    </li>
                                </ul>
                            </div>
                            <div>
                                <h4 className="text-lg font-semibold mb-4">Legal & Support</h4>
                                <ul className="space-y-3 text-gray-400">
                                    <li><a href="contact.html" className="hover:text-white transition-colors">Contact Us</a></li>
                                    <li><a href="privacy.html" className="hover:text-white transition-colors">Privacy Policy</a></li>
                                    <li><a href="terms.html" className="hover:text-white transition-colors">Terms & Conditions</a></li>
                                    <li><a href="refund.html" className="hover:text-white transition-colors">Refund Policy</a></li>
                                </ul>
                            </div>
                        </div>

                        <div className="border-t border-gray-800 pt-8">
                            <div className="flex flex-col md:flex-row justify-between items-center">
                                <div className="text-gray-400 mb-4 md:mb-0">
                                    <p>&copy; {new Date().getFullYear()} Bizma. All rights reserved.</p>
                                </div>
                                <div className="flex items-center space-x-6 text-sm text-gray-400">
                                    <span className="flex items-center">
                                        <i className="fas fa-shield-alt mr-2"></i>
                                        Secure & Reliable
                                    </span>
                                    <span className="flex items-center">
                                        <i className="fas fa-headset mr-2"></i>
                                        24/7 Support
                                    </span>
                                    <span className="flex items-center">
                                        <i className="fas fa-globe mr-2"></i>
                                        Made in India
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </footer>

                {/* Login Modal */}
                {showLoginModal && (
                    <Modal
                        isOpen={showLoginModal}
                        onClose={() => setShowLoginModal(false)}
                        title="Sign In to Bizma"
                        size="sm"
                    >
                        <LoginForm
                            onLogin={() => {
                                setShowLoginModal(false);
                                // Redirect to dashboard after login
                                const dashboardUrl = window.getAppUrl ? window.getAppUrl('/dashboard') : '/dashboard';
                                window.location.href = dashboardUrl;
                            }}
                            onSwitchToRegister={() => {
                                setShowLoginModal(false);
                                setShowRegisterModal(true);
                            }}
                            onSwitchToForgotPassword={() => {
                                setShowLoginModal(false);
                                setShowForgotPasswordModal(true);
                            }}
                        />
                    </Modal>
                )}

                {/* Register Modal */}
                {showRegisterModal && (
                    <Modal
                        isOpen={showRegisterModal}
                        onClose={() => setShowRegisterModal(false)}
                        title="Get Started with Bizma"
                        size="lg"
                    >
                        <EnhancedRegisterForm
                            onRegister={() => {
                                setShowRegisterModal(false);
                                // Show success message and redirect
                                if (window.toast) {
                                    window.toast.success('Registration successful! Please check your email to verify your account.');
                                }
                            }}
                            onSwitchToLogin={() => {
                                setShowRegisterModal(false);
                                setShowLoginModal(true);
                            }}
                        />
                    </Modal>
                )}

                {/* Forgot Password Modal */}
                {showForgotPasswordModal && (
                    <Modal
                        isOpen={showForgotPasswordModal}
                        onClose={() => setShowForgotPasswordModal(false)}
                        title="Reset Your Password"
                        size="sm"
                    >
                        <ForgotPasswordForm
                            onSuccess={() => {
                                setShowForgotPasswordModal(false);
                                if (window.toast) {
                                    window.toast.success('Password reset email sent! Please check your inbox.');
                                }
                            }}
                            onSwitchToLogin={() => {
                                setShowForgotPasswordModal(false);
                                setShowLoginModal(true);
                            }}
                        />
                    </Modal>
                )}
            </div>
        );
    } catch (error) {
        console.error('Landing page error:', error);
        // Report error to error handler if available
        if (window.ErrorHandler && window.ErrorHandler.handleError) {
            window.ErrorHandler.handleError(error);
        }
        return null;
    }
}
