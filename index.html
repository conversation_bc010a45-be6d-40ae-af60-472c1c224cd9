<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Comprehensive business management application for managing customers, contracts, invoices, and more">
    <meta name="keywords" content="business management, CRM, customer management, invoice, quotation, contract management">
    <meta property="og:title" content="Business Management Application">
    <meta property="og:description" content="Comprehensive business management application for managing customers, contracts, invoices, and more">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="Business Management Application">
    <meta name="twitter:description" content="Comprehensive business management application for managing customers, contracts, invoices, and more">
    <title>Business Management Application</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <link rel="alternate icon" type="image/x-icon" href="/favicon.ico">
    <!-- Production Tailwind CSS -->
    <link href="./styles/main.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <link href="styles/main.css" rel="stylesheet">
    <link href="styles/dashboard.css" rel="stylesheet">
    <link href="styles/customers.css" rel="stylesheet">
    <link href="styles/quotations.css" rel="stylesheet">
    <link href="styles/invoices.css" rel="stylesheet">
    <link href="styles/contracts.css" rel="stylesheet">
    <link href="styles/templates.css" rel="stylesheet">
    <link href="styles/items.css" rel="stylesheet">
    <link href="styles/settings.css" rel="stylesheet">
    <link href="styles/leads.css" rel="stylesheet">
    <link href="styles/subscriptions.css" rel="stylesheet">
    <link href="styles/reports.css" rel="stylesheet">

    <!-- Application Configuration -->
    <script src="config.js?v=20250709-final"></script>

    <!-- Core Libraries -->
    <script src="https://unpkg.com/@babel/standalone@7/babel.min.js"></script>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>

    <!-- Babel Configuration -->
    <script>
        // Configure Babel with modern presets and plugins
        Babel.registerPreset('custom', {
            presets: [
                ['react', {
                    runtime: 'automatic'
                }]
            ],
            plugins: [
                'proposal-class-properties',
                'proposal-optional-chaining',
                'proposal-nullish-coalescing-operator'
            ]
        });

        // Log available plugins for debugging
        console.log('Available Babel plugins:', Object.keys(Babel.availablePlugins || {}));

        window.__DEVELOPMENT__ = false;
        window.DEBUG = false;
        window.NODE_ENV = 'production';
    </script>

    <!-- Mock API removed for production - using real database API -->

    <script>
        // Production optimizations
        if (typeof console !== 'undefined') {
            // Disable console.log in production (keep errors)
            if (window.NODE_ENV === 'production') {
                console.log = function() {};
                console.debug = function() {};
                console.info = function() {};
            }
        }
        
        // Performance monitoring
        window.performance && window.performance.mark && window.performance.mark('app-start');
    </script>
</head>
<body>
    <div id="root"></div>
    <!-- Load utilities first (plain JavaScript) -->
    <script src="utils/errorHandler.js"></script>
    <script type="text/babel" data-preset="custom" src="utils/errorUtils.js"></script>
    <script type="text/babel" data-preset="custom" src="utils/api-utils.js?v=function-conflict-fix"></script>
    <script type="text/babel" data-preset="custom" src="utils/reportError.js"></script>
    
    <!-- Load other utilities (Babel transpiled) -->
    <script type="text/babel" data-preset="custom" src="utils/dateUtils.js"></script>
    <script type="text/babel" data-preset="custom" src="utils/formatUtils.js?v=currency-fix"></script>
    <script type="text/babel" data-preset="custom" src="utils/validationUtils.js"></script>
    <script type="text/babel" data-preset="custom" src="utils/pdfUtils.js"></script>
    <script type="text/babel" data-preset="custom" src="utils/securityUtils.js"></script>
    
    <!-- Common Components -->
    <script type="text/babel" data-preset="custom" src="components/common/Button.js"></script>
    <script type="text/babel" data-preset="custom" src="components/common/Input.js?v=null-fix"></script>
    <script type="text/babel" data-preset="custom" src="components/common/Modal.js"></script>
    <script type="text/babel" data-preset="custom" src="components/common/Table.js"></script>
    <script type="text/babel" data-preset="custom" src="components/common/Notification.js"></script>
    <script type="text/babel" data-preset="custom" src="components/common/SearchBar.js"></script>
    <script type="text/babel" data-preset="custom" src="components/common/Toast.js"></script>
    
    <!-- Authentication Components - Load AuthContext first -->
    <script type="text/babel" data-preset="custom" src="components/auth/AuthContext.js?v=context-unify"></script>
    <script type="text/babel" data-preset="custom" src="components/auth/LoginForm.js"></script>
    <script type="text/babel" data-preset="custom" src="components/auth/RegisterForm.js"></script>
    <script type="text/babel" data-preset="custom" src="components/auth/EnhancedRegisterForm.js"></script>
    <script type="text/babel" data-preset="custom" src="components/auth/ForgotPasswordForm.js"></script>
    <script type="text/babel" data-preset="custom" src="components/auth/ResetPasswordForm.js"></script>
    <script type="text/babel" data-preset="custom" src="components/auth/PrivateRoute.js"></script>
    <script type="text/babel" data-preset="custom" src="components/auth/UserProfile.js"></script>
    <script type="text/babel" data-preset="custom" src="components/auth/CompanySwitcher.js"></script>
    
    <!-- Layout Components -->
    <script type="text/babel" data-preset="custom" src="components/layout/Sidebar.js?v=2"></script>
    <script type="text/babel" data-preset="custom" src="components/layout/Header.js?v=2"></script>
    <script type="text/babel" data-preset="custom" src="components/layout/MainLayout.js?v=2"></script>
    <!-- Lead Management Components -->
    <script type="text/babel" data-preset="custom" src="components/leads/TagInput.js"></script>
    <script type="text/babel" data-preset="custom" src="components/leads/LeadForm.js?v=api-fix"></script>
    <script type="text/babel" data-preset="custom" src="components/leads/LeadList.js"></script>
    <script type="text/babel" data-preset="custom" src="components/leads/LeadDetails.js"></script>
    <script type="text/babel" data-preset="custom" src="components/leads/LeadActivity.js"></script>
    <script type="text/babel" data-preset="custom" src="components/leads/ActivityItem.js"></script>
    <script type="text/babel" data-preset="custom" src="components/leads/LeadNotes.js"></script>
    <script type="text/babel" data-preset="custom" src="components/leads/LeadTasks.js"></script>
    <!-- Subscription Components -->
    <script type="text/babel" data-preset="custom" src="components/subscriptions/PlanCard.js"></script>
    <script type="text/babel" data-preset="custom" src="components/subscriptions/UsageStats.js"></script>
    <script type="text/babel" data-preset="custom" src="components/subscriptions/UpgradeModal.js"></script>
    <script type="text/babel" data-preset="custom" src="components/subscriptions/CurrentPlan.js"></script>
    <!-- Other Components -->
    <script type="text/babel" data-preset="custom" src="components/customers/CustomerForm.js"></script>
    <script type="text/babel" data-preset="custom" src="components/customers/CustomerList.js"></script>
    <script type="text/babel" data-preset="custom" src="components/customers/CustomerDetails.js"></script>
    <script type="text/babel" data-preset="custom" src="components/quotations/QuotationForm.js?v=fixed-urls"></script>
    <script type="text/babel" data-preset="custom" src="components/quotations/QuotationList.js?v=fixed-urls"></script>
    <script type="text/babel" data-preset="custom" src="components/invoices/InvoiceForm.js"></script>
    <script type="text/babel" data-preset="custom" src="components/invoices/InvoiceList.js"></script>
    <script type="text/babel" data-preset="custom" src="components/contracts/ContractForm.js?v=api-fix"></script>
    <script type="text/babel" data-preset="custom" src="components/contracts/ContractList.js"></script>
    <script type="text/babel" data-preset="custom" src="components/items/ItemForm.js?v=api-fix"></script>
    <script type="text/babel" data-preset="custom" src="components/items/ItemList.js"></script>
    <script type="text/babel" data-preset="custom" src="components/items/CategoryForm.js?v=api-fix"></script>
    <script type="text/babel" data-preset="custom" src="components/items/CategoryList.js?v=api-fix"></script>
    <script type="text/babel" data-preset="custom" src="components/items/DigitalMarketingItems.js"></script>
    <script type="text/babel" data-preset="custom" src="components/reports/ReportsDashboard.js"></script>
    <script type="text/babel" data-preset="custom" src="components/templates/InvoiceTemplate.js?v=robust-template-fix"></script>
    <script type="text/babel" data-preset="custom" src="components/templates/QuotationTemplate.js?v=robust-template-fix"></script>
    <script type="text/babel" data-preset="custom" src="components/templates/ContractTemplate.js?v=robust-template-fix"></script>
    <script type="text/babel" data-preset="custom" src="components/documents/DocumentActions.js"></script>
    <script type="text/babel" data-preset="custom" src="components/documents/DocumentViewer.js"></script>
    <script type="text/babel" data-preset="custom" src="components/settings/CompanySettings.js?v=null-fix"></script>
    <script type="text/babel" data-preset="custom" src="components/settings/BrandingSettings.js"></script>
    <script type="text/babel" data-preset="custom" src="components/settings/TemplateSettings.js?v=margin-preset-fix"></script>
    <script type="text/babel" data-preset="custom" src="components/settings/PreferenceSettings.js"></script>
    <script type="text/babel" data-preset="custom" src="components/settings/NotificationSettings.js"></script>
    <script type="text/babel" data-preset="custom" src="components/settings/TemplatePreview.js"></script>
    <!-- Pages -->
    <script type="text/babel" data-preset="custom" src="pages/LandingPage.js"></script>
    <script type="text/babel" data-preset="custom" src="pages/Dashboard.js?v=currency-fix"></script>
    <script type="text/babel" data-preset="custom" src="pages/SuperAdminDashboard.js?v=boolean-fix"></script>
    <script type="text/babel" data-preset="custom" src="components/pages/ProfilePage.js"></script>
    <script type="text/babel" data-preset="custom" src="pages/Customers.js?v=api-fix"></script>
    <script type="text/babel" data-preset="custom" src="pages/Leads.js?v=api-fix"></script>
    <script type="text/babel" data-preset="custom" src="pages/Quotations.js?v=fixed-urls"></script>
    <script type="text/babel" data-preset="custom" src="pages/Invoices.js?v=fixed"></script>
    <script type="text/babel" data-preset="custom" src="pages/Contracts.js?v=fixed"></script>
    <script type="text/babel" data-preset="custom" src="pages/Items.js?v=fixed"></script>
    <script type="text/babel" data-preset="custom" src="pages/Reports.js?v=fixed"></script>
    <script type="text/babel" data-preset="custom" src="pages/Settings.js?v=fixed"></script>
    <script type="text/babel" data-preset="custom" src="pages/Subscriptions.js?v=fixed"></script>
    <script type="text/babel" data-preset="custom" src="pages/LandingPage.js?v=fixed"></script>
    <!-- Main App (loaded last) -->
    <script type="text/babel" data-preset="custom" src="app.js?v=context-unify"></script>
</body>
</html>
