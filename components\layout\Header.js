function Header({ user, onLogout, onMobileMenuToggle, isMobileMenuOpen }) {
    try {
        const [notifications, setNotifications] = React.useState([]);
        const [showNotifications, setShowNotifications] = React.useState(false);
        const [showUserMenu, setShowUserMenu] = React.useState(false);
        const [searchQuery, setSearchQuery] = React.useState('');
        const [searchResults, setSearchResults] = React.useState([]);
        const [isSearching, setIsSearching] = React.useState(false);
        const [showSearchResults, setShowSearchResults] = React.useState(false);
        
        // Refs for dropdown handling
        const notificationsRef = React.useRef(null);
        const userMenuRef = React.useRef(null);
        const searchRef = React.useRef(null);

        const handleSearch = (e) => {
            const query = e.target.value;
            setSearchQuery(query);
            
            if (query.length >= 2) {
                setIsSearching(true);
                setShowSearchResults(true);
                
                // Simulate search results - in a real app, this would call an API
                setTimeout(() => {
                    const results = [];
                    
                    // Search customers
                    if ('customer'.includes(query.toLowerCase())) {
                        results.push({
                            type: 'customer',
                            title: 'Customers',
                            items: [
                                { id: 'cust1', name: 'ABC Corporation', path: '/customers/cust1' },
                                { id: 'cust2', name: 'XYZ Industries', path: '/customers/cust2' }
                            ]
                        });
                    }
                    
                    // Search invoices
                    if ('invoice'.includes(query.toLowerCase())) {
                        results.push({
                            type: 'invoice',
                            title: 'Invoices',
                            items: [
                                { id: 'inv1', name: 'Invoice #INV-001', path: '/invoices/inv1' },
                                { id: 'inv2', name: 'Invoice #INV-002', path: '/invoices/inv2' }
                            ]
                        });
                    }
                    
                    setSearchResults(results);
                    setIsSearching(false);
                }, 500);
            } else {
                setShowSearchResults(false);
                setSearchResults([]);
            }
        };

        const handleSearchResultClick = (path) => {
            setShowSearchResults(false);
            setSearchQuery('');
            
            // Extract page and ID from path
            const parts = path.split('/').filter(p => p);
            if (parts.length >= 2) {
                window.dispatchEvent(new CustomEvent('app-navigate', { 
                    detail: { 
                        page: parts[0],
                        id: parts[1],
                        action: parts[2] || null,
                        params: {}
                    } 
                }));
            }
        };

        const toggleNotifications = () => {
            setShowNotifications(!showNotifications);
            // Close other dropdowns
            setShowUserMenu(false);
            setShowSearchResults(false);
        };

        const toggleUserMenu = () => {
            setShowUserMenu(!showUserMenu);
            // Close other dropdowns
            setShowNotifications(false);
            setShowSearchResults(false);
        };

        const handleLogout = async () => {
            try {
                // Call logout API
                await fetch('/api/api.php/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                    }
                });

                // Show success message
                if (window.toast) {
                    window.toast.success('Logged out successfully');
                }
            } catch (error) {
                console.error('Logout error:', error);
                if (window.toast) {
                    window.toast.error('Logout failed, but you will be signed out locally');
                }
            } finally {
                // Clear local storage and redirect
                localStorage.removeItem('authToken');
                localStorage.removeItem('rememberMe');
                if (onLogout) onLogout();
            }
        };

        // Enhanced click outside handler using refs
        React.useEffect(() => {
            const handleClickOutside = (event) => {
                // Handle notifications dropdown
                if (notificationsRef.current && !notificationsRef.current.contains(event.target)) {
                    setShowNotifications(false);
                }
                
                // Handle user menu dropdown
                if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
                    setShowUserMenu(false);
                }
                
                // Handle search results dropdown
                if (searchRef.current && !searchRef.current.contains(event.target)) {
                    setShowSearchResults(false);
                }
            };

            // Add event listener
            document.addEventListener('mousedown', handleClickOutside);
            
            // Keyboard navigation - close dropdowns on escape
            const handleEscKey = (event) => {
                if (event.key === 'Escape') {
                    setShowNotifications(false);
                    setShowUserMenu(false);
                    setShowSearchResults(false);
                }
            };
            
            document.addEventListener('keydown', handleEscKey);
            
            // Cleanup
            return () => {
                document.removeEventListener('mousedown', handleClickOutside);
                document.removeEventListener('keydown', handleEscKey);
            };
        }, []);

        return (
            <header data-name="main-header" className="bg-white shadow-sm sticky top-0 z-20">
                <div data-name="header-container" className="flex items-center justify-between px-4 md:px-6 py-3">
                    {/* Mobile menu toggle button */}
                    {onMobileMenuToggle && (
                        <button
                            onClick={onMobileMenuToggle}
                            className="lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                            aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
                        >
                            <i className={`fas ${isMobileMenuOpen ? 'fa-times' : 'fa-bars'}`}></i>
                        </button>
                    )}


                    
                    {/* Search container - hidden on small mobile */}
                    <div data-name="search-container" className="flex-1 max-w-xl mx-4 hidden sm:block" ref={searchRef}>
                        <div className="relative">
                            <input
                                type="text"
                                data-name="search-input"
                                placeholder="Search customers, invoices, etc..."
                                value={searchQuery}
                                onChange={handleSearch}
                                className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
                                aria-label="Search"
                            />
                            <i className="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                            
                            {/* Search results dropdown */}
                            {showSearchResults && (
                                <div className="absolute top-full left-0 right-0 mt-1 bg-white rounded-lg shadow-xl border border-gray-200 z-50 max-h-96 overflow-y-auto">
                                    <div className="p-3">
                                        {isSearching ? (
                                            <div className="flex justify-center items-center py-4">
                                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                                                <span className="ml-2 text-gray-600">Searching...</span>
                                            </div>
                                        ) : searchResults.length === 0 ? (
                                            <p className="text-gray-500 py-2">No results found for "{searchQuery}"</p>
                                        ) : (
                                            <div>
                                                {searchResults.map((group, groupIndex) => (
                                                    <div key={groupIndex} className="mb-3">
                                                        <h3 className="text-xs font-semibold text-gray-500 uppercase mb-1">{group.title}</h3>
                                                        <ul>
                                                            {group.items.map((item, itemIndex) => (
                                                                <li key={itemIndex}>
                                                                    <button
                                                                        onClick={() => handleSearchResultClick(item.path)}
                                                                        className="w-full text-left px-3 py-2 hover:bg-gray-50 rounded flex items-center"
                                                                    >
                                                                        <i className={`fas fa-${group.type === 'customer' ? 'user' : 'file-invoice'} text-gray-400 mr-2`}></i>
                                                                        <span>{item.name}</span>
                                                                    </button>
                                                                </li>
                                                            ))}
                                                        </ul>
                                                    </div>
                                                ))}
                                                <div className="pt-2 border-t border-gray-200">
                                                    <button
                                                        className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                                                        onClick={() => setShowSearchResults(false)}
                                                    >
                                                        Close
                                                    </button>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Header actions */}
                    <div data-name="header-actions" className="flex items-center space-x-1 md:space-x-4">
                        {/* Search button for mobile */}
                        <button
                            className="sm:hidden p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full"
                            onClick={() => alert('Mobile search not implemented')}
                            aria-label="Search"
                        >
                            <i className="fas fa-search"></i>
                        </button>
                        
                        {/* Notifications */}
                        <div className="relative" ref={notificationsRef}>
                            <button
                                data-name="notifications-button"
                                onClick={toggleNotifications}
                                className="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full"
                                aria-label="Notifications"
                            >
                                <i className="fas fa-bell"></i>
                                {notifications.length > 0 && (
                                    <span className="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
                                )}
                            </button>

                            {showNotifications && (
                                <div 
                                    data-name="notifications-dropdown" 
                                    className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-xl border border-gray-200 z-50"
                                >
                                    <div className="p-4">
                                        <div className="flex justify-between items-center mb-3">
                                            <h3 className="text-lg font-semibold">Notifications</h3>
                                            <button 
                                                className="text-gray-400 hover:text-gray-600"
                                                onClick={() => setShowNotifications(false)}
                                            >
                                                <i className="fas fa-times"></i>
                                            </button>
                                        </div>
                                        {notifications.length === 0 ? (
                                            <div className="py-6 text-center">
                                                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                                    <i className="fas fa-bell text-gray-400 text-xl"></i>
                                                </div>
                                                <p className="text-gray-500">No new notifications</p>
                                            </div>
                                        ) : (
                                            <ul className="space-y-2 max-h-64 overflow-y-auto">
                                                {notifications.map((notification, index) => (
                                                    <li key={index} className="p-2 hover:bg-gray-50 rounded">
                                                        {notification.message}
                                                    </li>
                                                ))}
                                            </ul>
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Help button */}
                        <button
                            data-name="help-button"
                            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full hidden md:block"
                            aria-label="Help"
                        >
                            <i className="fas fa-question-circle"></i>
                        </button>

                        {/* User Profile Dropdown */}
                        <div data-name="user-menu" className="relative" ref={userMenuRef}>
                            <button
                                onClick={toggleUserMenu}
                                className="flex items-center space-x-2 p-1 rounded-lg hover:bg-gray-100 transition-colors"
                                aria-label="User menu"
                            >
                                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                                    {(user && user.name) ? user.name.charAt(0).toUpperCase() : 'U'}
                                </div>
                                <div className="hidden md:block text-left">
                                    <div className="text-sm font-medium text-gray-900">
                                        {(user && user.name) || 'User'}
                                    </div>
                                    <div className="text-xs text-gray-500">
                                        {(user && user.company && user.company.name) || 'Company'}
                                    </div>
                                </div>
                                <i className="fas fa-chevron-down text-xs text-gray-400 hidden md:block"></i>
                            </button>

                            {showUserMenu && (
                                <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-xl border border-gray-200 z-50">
                                    <div className="p-4 border-b border-gray-200">
                                        <div className="flex items-center space-x-3">
                                            <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white text-lg font-medium">
                                                {(user && user.name) ? user.name.charAt(0).toUpperCase() : 'U'}
                                            </div>
                                            <div>
                                                <div className="font-medium text-gray-900">{(user && user.name) || 'User'}</div>
                                                <div className="text-sm text-gray-500">{(user && user.email) || '<EMAIL>'}</div>
                                                <div className="text-xs text-gray-400">{(user && user.company && user.company.name) || 'Company'}</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="py-2">
                                        {(user && user.role === 'super_admin') && (
                                            <button
                                                onClick={() => {
                                                    setShowUserMenu(false);
                                                    // Direct navigation to super admin dashboard
                                                    const url = window.getAppUrl('/super-admin');
                                                    console.log('Direct navigation to:', url);
                                                    window.location.href = url;
                                                }}
                                                className="w-full px-4 py-2 text-left text-sm text-red-700 hover:bg-red-50 flex items-center space-x-2 border-b border-gray-200"
                                            >
                                                <i className="fas fa-crown w-4"></i>
                                                <span>Super Admin</span>
                                            </button>
                                        )}

                                        <button
                                            onClick={() => {
                                                setShowUserMenu(false);
                                                // Navigate to profile page with improved routing
                                                window.dispatchEvent(new CustomEvent('app-navigate', { 
                                                    detail: { 
                                                        page: 'profile',
                                                        id: null,
                                                        action: null,
                                                        params: {}
                                                    } 
                                                }));
                                            }}
                                            className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
                                        >
                                            <i className="fas fa-user w-4"></i>
                                            <span>Profile Settings</span>
                                        </button>

                                        <button
                                            onClick={() => {
                                                setShowUserMenu(false);
                                                // Navigate to company settings with improved routing
                                                window.dispatchEvent(new CustomEvent('app-navigate', { 
                                                    detail: { 
                                                        page: 'settings',
                                                        id: null,
                                                        action: null,
                                                        params: {}
                                                    } 
                                                }));
                                            }}
                                            className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
                                        >
                                            <i className="fas fa-building w-4"></i>
                                            <span>Company Settings</span>
                                        </button>

                                        <button
                                            onClick={() => {
                                                setShowUserMenu(false);
                                                // Navigate to subscriptions page with improved routing
                                                window.dispatchEvent(new CustomEvent('app-navigate', { 
                                                    detail: { 
                                                        page: 'subscriptions',
                                                        id: null,
                                                        action: null,
                                                        params: {}
                                                    } 
                                                }));
                                            }}
                                            className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
                                        >
                                            <i className="fas fa-credit-card w-4"></i>
                                            <span>Subscription</span>
                                        </button>

                                        <hr className="my-2" />

                                        <button
                                            onClick={handleLogout}
                                            className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2"
                                        >
                                            <i className="fas fa-sign-out-alt w-4"></i>
                                            <span>Sign Out</span>
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </header>
        );
    } catch (error) {
        console.error('Header component error:', error);
        reportError(error);
        return null;
    }
}
