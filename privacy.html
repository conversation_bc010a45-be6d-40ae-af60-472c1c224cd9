<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Privacy Policy - Bizma</title>
    <meta name="description" content="Privacy Policy for Bizma - Business Management Platform">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    
    <!-- React -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    
    <!-- Babel for JSX -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
</head>
<body>
    <div id="root"></div>

    <!-- Load Privacy Policy Component -->
    <script type="text/babel" src="pages/PrivacyPolicy.js"></script>
    
    <script type="text/babel">
        const { createRoot } = ReactDOM;
        const root = createRoot(document.getElementById('root'));
        root.render(<PrivacyPolicy />);
    </script>
</body>
</html>
