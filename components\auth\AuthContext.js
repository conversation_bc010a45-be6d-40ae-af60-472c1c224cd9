const AuthContext = React.createContext();

// Make AuthContext and AuthProvider globally available
window.AuthContext = AuthContext;
window.AuthProvider = function AuthProvider({ children }) {
    try {
        const [user, setUser] = React.useState(null);
        const [loading, setLoading] = React.useState(true);
        const [error, setError] = React.useState(null);
        const [companies, setCompanies] = React.useState([]);
        const [currentCompany, setCurrentCompany] = React.useState(null);

        React.useEffect(() => {
            checkAuth();
        }, []);

        React.useEffect(() => {
            if (user) {
                fetchUserCompanies();
            }
        }, [user]);

        const checkAuth = async () => {
            try {
                const token = localStorage.getItem('authToken');
                if (!token) {
                    console.log('No auth token found');
                    setLoading(false);
                    return;
                }

                // Get token expiration time from JWT if possible
                let tokenExpired = false;
                try {
                    const tokenParts = token.split('.');
                    if (tokenParts.length === 3) {
                        const payload = JSON.parse(atob(tokenParts[1]));
                        if (payload.exp) {
                            // Check if token is expired or about to expire (within 5 minutes)
                            const expiryTime = payload.exp * 1000; // Convert to milliseconds
                            const currentTime = Date.now();
                            const timeToExpiry = expiryTime - currentTime;
                            
                            if (timeToExpiry <= 0) {
                                tokenExpired = true;
                            } else if (timeToExpiry < 5 * 60 * 1000) {
                                // Token will expire in less than 5 minutes, try to refresh it
                                await refreshToken(token);
                                return;
                            }
                        }
                    }
                } catch (e) {
                    console.warn('Error parsing token:', e);
                    // Continue with verification even if parsing fails
                }

                // If token is already known to be expired, skip verification
                if (tokenExpired) {
                    console.log('Token is expired, removing from storage');
                    localStorage.removeItem('authToken');
                    setLoading(false);
                    return;
                }

                // Verify token with server - use simple auth endpoint
                const authUrl = window.APP_CONFIG.BASE_PATH + '/api/verify-token.php';
                console.log('Auth verification URL:', authUrl);
                const response = await fetch(authUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const responseText = await response.text();
                    console.log('Auth response text:', responseText);

                    let data;
                    try {
                        data = JSON.parse(responseText);
                    } catch (parseError) {
                        console.error('Failed to parse auth response as JSON:', parseError);
                        console.error('Response text:', responseText);
                        throw new Error('Server returned invalid JSON response');
                    }

                    if (data.user) {
                        setUser(data.user);

                        // If token was refreshed, update it
                        if (data.token) {
                            localStorage.setItem('authToken', data.token);
                        }
                    } else {
                        throw new Error('Invalid user data received');
                    }
                } else {
                    // Handle different error cases
                    if (response.status === 401 || response.status === 403) {
                        // Token is invalid or expired
                        localStorage.removeItem('authToken');
                        throw new Error('Authentication token is invalid or expired');
                    } else {
                        // Server error or other issue
                        throw new Error(`Server error: ${response.status}`);
                    }
                }
            } catch (error) {
                console.error('Auth check error:', error);
                localStorage.removeItem('authToken');
                setError(error.message || 'Authentication failed');
            } finally {
                setLoading(false);
            }
        };
        
        // Add token refresh functionality
        const refreshToken = async (currentToken) => {
            try {
                console.log('Attempting to refresh token');
                const refreshUrl = window.getApiUrl('/auth/refresh');
                const response = await fetch(refreshUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.token) {
                        localStorage.setItem('authToken', data.token);
                        if (data.user) {
                            setUser(data.user);
                        }
                        console.log('Token refreshed successfully');
                        return true;
                    }
                }
                return false;
            } catch (error) {
                console.error('Token refresh error:', error);
                return false;
            } finally {
                setLoading(false);
            }
        };

        const fetchUserCompanies = async () => {
            try {
                // If user has company_id, create currentCompany object directly
                if (user.company_id) {
                    const currentCompanyObj = {
                        objectId: user.company_id,
                        objectType: 'company',
                        objectData: {
                            name: user.company_name || 'Default Company',
                            ownerId: user.id || user.object_id,
                            status: 'active'
                        }
                    };
                    setCurrentCompany(currentCompanyObj);
                    setCompanies([currentCompanyObj]);
                    return;
                }

                // Fallback to original logic if no company_id
                const response = await trickleListObjects('company', 100, true);
                const userCompanies = response.items.filter(
                    company => company.objectData.ownerId === user.id ||
                             (company.objectData.members && company.objectData.members.includes(user.id))
                );
                setCompanies(userCompanies);

                // Set current company
                const lastCompanyId = localStorage.getItem('lastCompanyId');
                if (lastCompanyId) {
                    const lastCompany = userCompanies.find(c => c.objectId === lastCompanyId);
                    if (lastCompany) {
                        setCurrentCompany(lastCompany);
                        return;
                    }
                }
                // Default to first company if no last company found
                if (userCompanies.length > 0) {
                    setCurrentCompany(userCompanies[0]);
                }
            } catch (error) {
                console.error('Error fetching user companies:', error);
                setError('Failed to load companies');
            }
        };

        const login = async (email, password, rememberMe) => {
            try {
                setLoading(true);
                setError(null);
                
                // Use the API utility for better error handling and retry logic
                const loginUrl = window.getApiUrl('/auth/login');
                
                const response = await fetch(loginUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email,
                        password,
                        rememberMe: !!rememberMe
                    })
                });
                
                // Handle non-OK responses
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    const errorMessage = errorData.message || errorData.error || `Login failed with status ${response.status}`;
                    
                    // Log specific error codes for debugging
                    if (response.status === 401) {
                        console.warn('Authentication failed: Invalid credentials');
                    } else if (response.status === 429) {
                        console.warn('Rate limit exceeded: Too many login attempts');
                    }
                    
                    throw new Error(errorMessage);
                }
                
                // Process successful response
                const data = await response.json();
                
                if (data.token) {
                    // Store authentication data
                    localStorage.setItem('authToken', data.token);
                    if (rememberMe) {
                        localStorage.setItem('rememberMe', 'true');
                    }
                    
                    // Update state
                    setUser(data.user);
                    
                    // Return success response
                    return { 
                        success: true, 
                        user: data.user, 
                        token: data.token,
                        message: data.message || 'Login successful'
                    };
                } else {
                    throw new Error('Invalid response: No authentication token received');
                }
            } catch (error) {
                console.error('Login error:', error);
                setError(error.message || 'Invalid email or password');
                throw error;
            } finally {
                setLoading(false);
            }
        };

        const register = async (userData) => {
            try {
                setLoading(true);
                const response = await trickleCreateObject('auth', {
                    type: 'register',
                    ...userData
                });

                if (response.objectData.token) {
                    localStorage.setItem('authToken', response.objectData.token);
                    setUser(response.objectData.user);
                    return response.objectData.user;
                }
            } catch (error) {
                console.error('Registration error:', error);
                throw new Error('Registration failed');
            } finally {
                setLoading(false);
            }
        };

        const logout = async (redirectToLogin = true) => {
            try {
                setLoading(true);
                const token = localStorage.getItem('authToken');
                
                if (token) {
                    // Call logout API to invalidate token on server
                    const logoutUrl = window.getApiUrl('/auth/logout');
                    await fetch(logoutUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        }
                    }).catch(err => {
                        // Log but don't block logout on server error
                        console.warn('Logout API error:', err);
                    });
                }
            } catch (error) {
                console.error('Logout error:', error);
            } finally {
                // Clear all auth-related data from storage
                localStorage.removeItem('authToken');
                localStorage.removeItem('rememberMe');
                localStorage.removeItem('lastCompanyId');
                
                // Clear application cache
                if (window.clearApiCache) {
                    window.clearApiCache();
                }
                
                // Reset state
                setUser(null);
                setCurrentCompany(null);
                setCompanies([]);
                setLoading(false);
                
                // Redirect to login page if requested
                if (redirectToLogin) {
                    // Use app navigation instead of direct location change
                    window.dispatchEvent(new CustomEvent('app-navigate', { 
                        detail: { 
                            page: 'login',
                            id: null,
                            action: null,
                            params: {}
                        } 
                    }));
                }
                
                return { success: true };
            }
        };

        const switchCompany = (companyId) => {
            const company = companies.find(c => c.objectId === companyId);
            if (company) {
                setCurrentCompany(company);
                localStorage.setItem('lastCompanyId', companyId);
            }
        };

        const updateUserProfile = async (profileData) => {
            try {
                const response = await trickleUpdateObject('user', user.id, profileData);
                setUser(response.objectData);
                return response.objectData;
            } catch (error) {
                console.error('Profile update error:', error);
                throw new Error('Failed to update profile');
            }
        };

        const createCompany = async (companyData) => {
            try {
                const response = await trickleCreateObject('company', {
                    ...companyData,
                    ownerId: user.id,
                    status: 'active',
                    createdAt: new Date().toISOString()
                });
                await fetchUserCompanies();
                return response.objectData;
            } catch (error) {
                console.error('Company creation error:', error);
                throw new Error('Failed to create company');
            }
        };

        const updateCompany = async (companyId, companyData) => {
            try {
                const response = await trickleUpdateObject('company', companyId, companyData);
                await fetchUserCompanies();
                return response.objectData;
            } catch (error) {
                console.error('Company update error:', error);
                throw new Error('Failed to update company');
            }
        };

        // Compute additional auth state properties
        const isAuthenticated = !!user;
        const token = localStorage.getItem('authToken');
        const userRole = (user && user.role) || 'user';
        const isAdmin = userRole === 'admin' || userRole === 'super_admin';
        const isSuperAdmin = userRole === 'super_admin';
        
        // Enhanced auth context value with more useful properties
        const value = {
            // User state
            user,
            isAuthenticated,
            token,
            userRole,
            isAdmin,
            isSuperAdmin,
            
            // UI state
            loading,
            error,
            
            // Company state
            companies,
            currentCompany,
            
            // Auth methods
            login,
            register,
            logout,
            refreshToken,
            
            // User and company methods
            switchCompany,
            updateUserProfile,
            createCompany,
            updateCompany,
            
            // Helper methods
            clearError: () => setError(null),
            hasPermission: (permission) => {
                // Simple permission check based on role
                if (!user) return false;
                if (isSuperAdmin) return true;
                
                // Add more sophisticated permission checks here
                const rolePermissions = {
                    admin: ['manage_users', 'manage_settings', 'view_reports', 'manage_billing'],
                    manager: ['view_reports', 'manage_customers', 'manage_invoices'],
                    user: ['view_customers', 'view_invoices']
                };
                
                return (rolePermissions[userRole] && rolePermissions[userRole].includes(permission)) || false;
            }
        };

        return (
            <AuthContext.Provider value={value}>
                {!loading && children}
            </AuthContext.Provider>
        );
    } catch (error) {
        console.error('AuthProvider error:', error);
        reportError(error);
        return null;
    }
}

function useAuth() {
    const context = React.useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}
