function QuotationTemplate({ quotation, companyInfo }) {
    try {
        // Check for customer data and handle if it's just an ID
        const [customerData, setCustomerData] = React.useState(null);
        const [loading, setLoading] = React.useState(true);

        // Extract template settings from companyInfo
        const templates = companyInfo && companyInfo.templates ? companyInfo.templates : {
            paperSize: 'a4',
            orientation: 'portrait',
            margins: { top: 15, right: 15, bottom: 15, left: 15 },
            quotationTemplate: 'classic',
            headerColor: '#3b82f6',
            accentColor: '#1e3a8a',
            fontFamily: 'Segoe UI, sans-serif',
            fontSize: 'medium',
            showLogo: true,
            showSignature: true,
            showWatermark: true
        };

        // Generate CSS classes for A4 styling
        const getA4Classes = () => {
            const classes = ['a4-page'];

            // Add margin class
            if (templates.margins) {
                const { top, right, bottom, left } = templates.margins;
                if (top === 10 && right === 10 && bottom === 10 && left === 10) {
                    classes.push('margin-minimal');
                } else if (top === 20 && right === 20 && bottom === 20 && left === 20) {
                    classes.push('margin-comfortable');
                } else if (right === 25 && left === 25) {
                    classes.push('margin-wide');
                } else {
                    classes.push('margin-standard');
                }
            }

            // Add font size class
            classes.push(`font-${templates.fontSize || 'medium'}`);

            // Add line height class
            classes.push(`line-height-${templates.lineHeight || 'normal'}`);

            // Add border style class
            classes.push(`border-${templates.borderStyle || 'minimal'}`);

            // Add page numbers class
            if (templates.showPageNumbers !== false) {
                classes.push('show-page-numbers');
            }

            return classes.join(' ');
        };

        React.useEffect(() => {
            async function fetchCustomerData() {
                try {
                    const quotationData = quotation.objectData || quotation;
                    if (typeof quotationData.customer === 'string' || quotationData.customer instanceof String) {
                        // If customer is just an ID, fetch the customer data
                        const customer = await trickleGetObject('customer', quotationData.customer);
                        setCustomerData(customer.objectData);
                    } else {
                        // If customer is already an object
                        setCustomerData(quotationData.customer);
                    }
                } catch (error) {
                    console.error('Error fetching customer data:', error);
                } finally {
                    setLoading(false);
                }
            }
            
            fetchCustomerData();
        }, [quotation]);

        // Format status text with first letter capitalized
        const getStatusText = (status) => {
            if (!status) return 'Draft';
            return status.charAt(0).toUpperCase() + status.slice(1);
        };

        // Calculate all amounts to ensure they're displayed correctly
        const calculateSubtotal = () => {
            const quotationData = quotation.objectData || quotation;
            if (quotationData.subtotal) return quotationData.subtotal;
            
            // Calculate from items if subtotal not provided
            if (quotationData.items && Array.isArray(quotationData.items)) {
                return quotationData.items.reduce((total, item) => 
                    total + (item.quantity * item.price), 0
                );
            }
            
            return 0;
        };

        const calculateTax = () => {
            const quotationData = quotation.objectData || quotation;
            if (quotationData.tax) return quotationData.tax;
            
            const subtotal = calculateSubtotal();
            const discount = parseFloat(quotationData.discount) || 0;
            const taxRate = parseFloat(quotationData.taxRate) || 0;
            
            return (subtotal - discount) * (taxRate / 100);
        };

        const calculateTotal = () => {
            const quotationData = quotation.objectData || quotation;
            if (quotationData.total) return quotationData.total;
            
            const subtotal = calculateSubtotal();
            const discount = parseFloat(quotationData.discount) || 0;
            const tax = calculateTax();
            
            return subtotal - discount + tax;
        };

        // Apply template settings to style
        const getTemplateStyles = () => {
            let style = {};
            
            // Font family
            if (templates.fontFamily) {
                style.fontFamily = templates.fontFamily;
            }
            
            // Font size
            const fontSizeMap = {
                'small': '0.875rem',
                'medium': '1rem',
                'large': '1.125rem'
            };
            style.fontSize = fontSizeMap[templates.fontSize] || '1rem';
            
            // Colors
            style.headerColor = templates.headerColor || '#3b82f6';
            style.accentColor = templates.accentColor || '#1e3a8a';
            
            // Paper size
            const paperSizeClass = templates.paperSize || 'a4';
            const orientationClass = templates.orientation || 'portrait';
            
            return {
                style,
                paperSizeClass,
                orientationClass
            };
        };

        const { style, paperSizeClass, orientationClass } = getTemplateStyles();

        if (loading) {
            return (
                <div className="flex justify-center items-center p-8">
                    <i className="fas fa-spinner fa-spin fa-2x text-blue-500"></i>
                </div>
            );
        }

        const quotationData = quotation.objectData || quotation;
        
        // Get status for watermark
        const isDraft = !quotationData.status || quotationData.status.toLowerCase() === 'draft';
        const isAccepted = quotationData.status && quotationData.status.toLowerCase() === 'accepted';
        
        return (
            <div 
                data-name="quotation-template" 
                className={`template-container ${getA4Classes()} ${orientationClass}`}
                style={style}
            >
                {templates.showWatermark && isDraft && <div className="watermark">DRAFT</div>}
                {templates.showWatermark && isAccepted && <div className="watermark">ACCEPTED</div>}
                
                {/* Header */}
                <div
                    className={`template-header style-${templates.headerStyle || 'modern'} flex justify-between items-start mb-8`}
                    style={{ borderColor: style.headerColor }}
                >
                    <div>
                        <h1 
                            className="template-title"
                            style={{ color: style.accentColor }}
                        >
                            QUOTATION
                        </h1>
                        <p className="template-number">#{quotationData.quotationNumber || quotation.objectId}</p>
                    </div>
                    <div className="company-details">
                        {templates.showLogo && companyInfo.logo && (
                            <img
                                src={companyInfo.logo}
                                alt="Company Logo"
                                className={`company-logo position-${templates.logoPosition || 'top-right'} size-${templates.logoSize || 'medium'} h-16 mb-4`}
                            />
                        )}
                        <h2 
                            className="company-name"
                            style={{ color: style.accentColor }}
                        >
                            {companyInfo.companyName}
                        </h2>
                        <p>{companyInfo.companyAddress}</p>
                        <p>{companyInfo.companyPhone}</p>
                        <p>{companyInfo.companyEmail}</p>
                        {companyInfo.companyWebsite && <p>{companyInfo.companyWebsite}</p>}
                        {companyInfo.companyGST && <p>GSTIN: {companyInfo.companyGST}</p>}
                    </div>
                </div>

                {/* Client & Quotation Info */}
                <div className="info-grid">
                    <div className="info-section">
                        <h3>Prepared For</h3>
                        {customerData ? (
                            <div>
                                <h4 className="text-lg font-bold text-gray-800 mb-1">{customerData.name}</h4>
                                {customerData.company && <p>{customerData.company}</p>}
                                {customerData.address && <p className="whitespace-pre-line">{customerData.address}</p>}
                                {customerData.email && <p>{customerData.email}</p>}
                                {customerData.phone && <p>{customerData.phone}</p>}
                                {customerData.gst && <p>GSTIN: {customerData.gst}</p>}
                            </div>
                        ) : (
                            <p className="text-gray-600">Customer information not available</p>
                        )}
                    </div>
                    <div className="info-section">
                        <div className="grid grid-cols-2 gap-4">
                            <div className="info-item">
                                <h4>Quotation Number</h4>
                                <p className="font-medium">{quotationData.quotationNumber || quotation.objectId.substring(0, 8)}</p>
                            </div>
                            <div className="info-item">
                                <h4>Quotation Date</h4>
                                <p>{formatDate(quotation.createdAt)}</p>
                            </div>
                            <div className="info-item">
                                <h4>Valid Until</h4>
                                <p>{formatDate(quotationData.validUntil)}</p>
                            </div>
                            <div className="info-item">
                                <h4>Status</h4>
                                <p>
                                    <span className={`status-badge status-${quotationData.status || 'draft'}`}>
                                        {getStatusText(quotationData.status)}
                                    </span>
                                </p>
                            </div>
                            {quotationData.referenceNumber && (
                                <div className="info-item">
                                    <h4>Reference Number</h4>
                                    <p>{quotationData.referenceNumber}</p>
                                </div>
                            )}
                            {quotationData.projectName && (
                                <div className="info-item">
                                    <h4>Project Name</h4>
                                    <p>{quotationData.projectName}</p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                {/* Items */}
                <div className="items-container">
                    <table className={`items-table style-${templates.tableStyle || 'striped'} w-full border-collapse`}>
                        <thead>
                            <tr style={{ backgroundColor: `${style.headerColor}10` }}>
                                <th className="w-6/12">Description</th>
                                <th className="w-1/12 text-center">Qty</th>
                                <th className="w-2/12 text-right">Unit Price</th>
                                <th className="w-3/12 text-right">Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            {quotationData.items && quotationData.items.map((item, index) => (
                                <tr key={index}>
                                    <td>
                                        <div className="font-medium">{item.description}</div>
                                        {item.details && <div className="text-gray-500 text-xs mt-1">{item.details}</div>}
                                    </td>
                                    <td className="text-center">{item.quantity}</td>
                                    <td className="text-right">{formatCurrency(item.price)}</td>
                                    <td className="text-right font-medium">{formatCurrency(item.quantity * item.price)}</td>
                                </tr>
                            ))}
                            
                            {/* Empty rows to make the table look better */}
                            {quotationData.items && quotationData.items.length < 5 && (
                                Array(5 - quotationData.items.length).fill().map((_, index) => (
                                    <tr key={`empty-${index}`} className="empty-row">
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                    </tr>
                                ))
                            )}
                        </tbody>
                    </table>
                </div>

                {/* Totals */}
                <div className="totals-section">
                    <div className="totals-table">
                        <div className="totals-row">
                            <span className="totals-label">Subtotal</span>
                            <span className="totals-value">{formatCurrency(calculateSubtotal())}</span>
                        </div>
                        
                        {(quotationData.discount > 0) && (
                            <div className="totals-row">
                                <span className="totals-label">Discount</span>
                                <span className="totals-value">-{formatCurrency(quotationData.discount)}</span>
                            </div>
                        )}
                        
                        {(quotationData.taxRate > 0 || quotationData.tax > 0) && (
                            <div className="totals-row">
                                <span className="totals-label">Tax ({quotationData.taxRate || 0}%)</span>
                                <span className="totals-value">{formatCurrency(calculateTax())}</span>
                            </div>
                        )}
                        
                        <div className="totals-row grand-total">
                            <span className="grand-total-label">Total</span>
                            <span 
                                className="grand-total-value"
                                style={{ color: style.accentColor }}
                            >
                                {formatCurrency(calculateTotal())}
                            </span>
                        </div>
                    </div>
                </div>

                {/* Notes & Terms */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    {quotationData.notes && (
                        <div className="notes-section">
                            <h3>Notes</h3>
                            <div className="notes-content">{quotationData.notes}</div>
                        </div>
                    )}
                    {quotationData.terms && (
                        <div className="notes-section">
                            <h3>Terms & Conditions</h3>
                            <div className="notes-content">{quotationData.terms}</div>
                        </div>
                    )}
                </div>

                {/* Validity & Acceptance */}
                <div className="quotation-validity mt-6">
                    <p>This quotation is valid until <strong>{formatDate(quotationData.validUntil)}</strong>.</p>
                    <p>To accept this quotation, please sign below or contact us.</p>
                </div>

                {/* Signature */}
                {templates.showSignature && (
                    <div className="signature-section">
                        <div className="signature-box">
                            <div className="signature-line">
                                {companyInfo.signature && (
                                    <img 
                                        src={companyInfo.signature} 
                                        alt="Authorized Signature" 
                                        className="signature-image"
                                    />
                                )}
                            </div>
                            <p className="signature-name">{companyInfo.authorizedName || companyInfo.companyName}</p>
                            <p className="signature-title">Authorized Signatory</p>
                        </div>
                        
                        <div className="signature-box">
                            <div className="signature-line">
                                {isAccepted && (
                                    <img 
                                        src="https://upload.wikimedia.org/wikipedia/commons/a/ac/Green_tick.png" 
                                        alt="Accepted" 
                                        className="signature-image"
                                        style={{ maxHeight: '3rem' }}
                                    />
                                )}
                            </div>
                            <p className="signature-name">Customer Acceptance</p>
                            <p className="signature-title">Date: _______________</p>
                        </div>
                    </div>
                )}

                {/* Footer */}
                <div className="template-footer">
                    <p className="font-medium">Thank you for your business!</p>
                    <p>If you have any questions concerning this quotation, please contact us.</p>
                    {companyInfo.companyWebsite && <p>{companyInfo.companyWebsite}</p>}
                </div>
            </div>
        );
    } catch (error) {
        console.error('QuotationTemplate component error:', error);
        reportError(error);
        return (
            <div className="bg-red-50 border border-red-200 text-red-800 p-4 rounded">
                Error rendering quotation template. Please try again.
            </div>
        );
    }
}
