function EmailSettings({ formData, handleEmailSettingsChange }) {
    const [emailSettings, setEmailSettings] = React.useState({
        smtp_host: '',
        smtp_port: '587',
        smtp_username: '',
        smtp_password: '',
        smtp_encryption: 'tls',
        from_email: '',
        from_name: '',
        reply_to: '',
        enabled: false,
        test_email: '',
        ...formData.emailSettings
    });

    const [testing, setTesting] = React.useState(false);
    const [testResult, setTestResult] = React.useState(null);
    const [showPassword, setShowPassword] = React.useState(false);

    const handleChange = (field, value) => {
        const newSettings = { ...emailSettings, [field]: value };
        setEmailSettings(newSettings);
        if (handleEmailSettingsChange) {
            handleEmailSettingsChange(newSettings);
        }
    };

    const testEmailConfiguration = async () => {
        if (!emailSettings.test_email) {
            setTestResult({ success: false, message: 'Please enter a test email address' });
            return;
        }

        setTesting(true);
        setTestResult(null);

        try {
            const response = await fetch(window.getApiUrl('/settings/test-email'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                },
                body: JSON.stringify({
                    emailSettings: emailSettings,
                    testEmail: emailSettings.test_email
                })
            });

            const result = await response.json();
            setTestResult(result);
        } catch (error) {
            setTestResult({ 
                success: false, 
                message: 'Failed to test email configuration: ' + error.message 
            });
        } finally {
            setTesting(false);
        }
    };

    return (
        <div className="space-y-6">
            <div className="border-b border-gray-200 pb-4">
                <h3 className="text-lg font-medium text-gray-900">Email Configuration</h3>
                <p className="text-sm text-gray-500 mt-1">
                    Configure SMTP settings for sending emails (notifications, invoices, quotations, etc.)
                </p>
            </div>

            {/* Enable/Disable Email */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                    <h4 className="text-sm font-medium text-gray-700">Enable Email Sending</h4>
                    <p className="text-sm text-gray-500">Turn on/off email functionality</p>
                </div>
                <div className="flex items-center">
                    <input
                        type="checkbox"
                        id="email-enabled"
                        checked={emailSettings.enabled}
                        onChange={(e) => handleChange('enabled', e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="email-enabled" className="ml-2 block text-sm text-gray-900">
                        {emailSettings.enabled ? 'Enabled' : 'Disabled'}
                    </label>
                </div>
            </div>

            {/* SMTP Configuration */}
            <div className="space-y-4">
                <h4 className="text-md font-medium text-gray-900">SMTP Server Settings</h4>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            SMTP Host *
                        </label>
                        <input
                            type="text"
                            value={emailSettings.smtp_host}
                            onChange={(e) => handleChange('smtp_host', e.target.value)}
                            placeholder="smtp.gmail.com"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            SMTP Port *
                        </label>
                        <select
                            value={emailSettings.smtp_port}
                            onChange={(e) => handleChange('smtp_port', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="25">25 (Non-encrypted)</option>
                            <option value="587">587 (TLS)</option>
                            <option value="465">465 (SSL)</option>
                            <option value="2525">2525 (Alternative)</option>
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Username *
                        </label>
                        <input
                            type="text"
                            value={emailSettings.smtp_username}
                            onChange={(e) => handleChange('smtp_username', e.target.value)}
                            placeholder="<EMAIL>"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Password *
                        </label>
                        <div className="relative">
                            <input
                                type={showPassword ? "text" : "password"}
                                value={emailSettings.smtp_password}
                                onChange={(e) => handleChange('smtp_password', e.target.value)}
                                placeholder="Your SMTP password"
                                className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                            <button
                                type="button"
                                onClick={() => setShowPassword(!showPassword)}
                                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                            >
                                {showPassword ? '👁️' : '👁️‍🗨️'}
                            </button>
                        </div>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Encryption
                        </label>
                        <select
                            value={emailSettings.smtp_encryption}
                            onChange={(e) => handleChange('smtp_encryption', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="none">None</option>
                            <option value="tls">TLS</option>
                            <option value="ssl">SSL</option>
                        </select>
                    </div>
                </div>
            </div>

            {/* Email Identity */}
            <div className="space-y-4">
                <h4 className="text-md font-medium text-gray-900">Email Identity</h4>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            From Email *
                        </label>
                        <input
                            type="email"
                            value={emailSettings.from_email}
                            onChange={(e) => handleChange('from_email', e.target.value)}
                            placeholder="<EMAIL>"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            From Name
                        </label>
                        <input
                            type="text"
                            value={emailSettings.from_name}
                            onChange={(e) => handleChange('from_name', e.target.value)}
                            placeholder="Bizma Business Management"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>

                    <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Reply-To Email
                        </label>
                        <input
                            type="email"
                            value={emailSettings.reply_to}
                            onChange={(e) => handleChange('reply_to', e.target.value)}
                            placeholder="<EMAIL>"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                </div>
            </div>

            {/* Test Email Configuration */}
            <div className="space-y-4 p-4 bg-blue-50 rounded-lg">
                <h4 className="text-md font-medium text-gray-900">Test Email Configuration</h4>
                <p className="text-sm text-gray-600">
                    Send a test email to verify your SMTP settings are working correctly.
                </p>
                
                <div className="flex gap-3">
                    <input
                        type="email"
                        value={emailSettings.test_email}
                        onChange={(e) => handleChange('test_email', e.target.value)}
                        placeholder="<EMAIL>"
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <button
                        onClick={testEmailConfiguration}
                        disabled={testing || !emailSettings.enabled}
                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                    >
                        {testing ? 'Testing...' : 'Send Test Email'}
                    </button>
                </div>

                {testResult && (
                    <div className={`p-3 rounded-md ${testResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                        {testResult.message}
                    </div>
                )}
            </div>

            {/* Common SMTP Providers */}
            <div className="space-y-3">
                <h4 className="text-md font-medium text-gray-900">Common SMTP Providers</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                    <div className="p-3 bg-gray-50 rounded">
                        <strong>Gmail:</strong><br/>
                        Host: smtp.gmail.com<br/>
                        Port: 587 (TLS)<br/>
                        Note: Use App Password
                    </div>
                    <div className="p-3 bg-gray-50 rounded">
                        <strong>Outlook/Hotmail:</strong><br/>
                        Host: smtp-mail.outlook.com<br/>
                        Port: 587 (TLS)
                    </div>
                    <div className="p-3 bg-gray-50 rounded">
                        <strong>Yahoo:</strong><br/>
                        Host: smtp.mail.yahoo.com<br/>
                        Port: 587 (TLS)
                    </div>
                    <div className="p-3 bg-gray-50 rounded">
                        <strong>SendGrid:</strong><br/>
                        Host: smtp.sendgrid.net<br/>
                        Port: 587 (TLS)
                    </div>
                </div>
            </div>
        </div>
    );
}
