function TemplateSettings({ formData, handleTemplateChange, handleMarginChange, handleOpenPreview }) {
    try {
        // Ensure templates object exists with default values
        const templates = formData.templates || {
            paperSize: 'a4',
            orientation: 'portrait',
            margins: { top: 15, right: 15, bottom: 15, left: 15 },
            invoiceTemplate: 'modern',
            quotationTemplate: 'classic',
            contractTemplate: 'professional',
            headerColor: '#3b82f6',
            accentColor: '#1e3a8a',
            fontFamily: 'Segoe UI, sans-serif',
            fontSize: 'medium',
            showLogo: true,
            showSignature: true,
            showWatermark: true,
            // Enhanced A4 specific settings
            lineHeight: 'normal',
            headerStyle: 'modern',
            footerText: '',
            showPageNumbers: true,
            showCompanyDetails: true,
            showBankDetails: true,
            logoPosition: 'top-right',
            logoSize: 'medium',
            borderStyle: 'minimal',
            tableStyle: 'striped'
        };

        // Ensure margins object exists
        const margins = templates.margins || { top: 15, right: 15, bottom: 15, left: 15 };

        // A4 Paper size presets for better formatting
        const paperSizePresets = {
            'a4-minimal': { top: 10, right: 10, bottom: 10, left: 10 },
            'a4-standard': { top: 15, right: 15, bottom: 15, left: 15 },
            'a4-comfortable': { top: 20, right: 20, bottom: 20, left: 20 },
            'a4-wide': { top: 15, right: 25, bottom: 15, left: 25 }
        };

        const handlePresetMargins = (preset) => {
            if (!preset) return; // Don't do anything if empty value selected

            const presetMargins = paperSizePresets[preset];
            if (presetMargins) {
                // Apply all margin values
                Object.keys(presetMargins).forEach(key => {
                    handleMarginChange({ target: { name: key, value: presetMargins[key] } });
                });

                // Reset the dropdown to show "Select Preset..." after applying
                setTimeout(() => {
                    const selectElement = document.querySelector('select[data-margin-preset]');
                    if (selectElement) {
                        selectElement.value = '';
                    }
                }, 100);

                console.log(`Applied ${preset} margins:`, presetMargins);
            }
        };

        // Detect current margin preset
        const getCurrentPreset = () => {
            const { top, right, bottom, left } = margins;

            if (top === 10 && right === 10 && bottom === 10 && left === 10) {
                return 'a4-minimal';
            } else if (top === 15 && right === 15 && bottom === 15 && left === 15) {
                return 'a4-standard';
            } else if (top === 20 && right === 20 && bottom === 20 && left === 20) {
                return 'a4-comfortable';
            } else if (top === 15 && right === 25 && bottom === 15 && left === 25) {
                return 'a4-wide';
            }
            return 'custom';
        };

        const currentPreset = getCurrentPreset();

        return (
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <h2 className="text-lg font-medium">Document Templates</h2>
                    <div className="text-sm text-gray-500 bg-blue-50 px-3 py-1 rounded-full">
                        <i className="fas fa-info-circle mr-1"></i>
                        Optimized for A4 Professional Printing
                    </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div className="col-span-1 md:col-span-3">
                        <h3 className="text-md font-medium text-gray-700 mb-2">Document Preview</h3>
                        <div className="flex flex-wrap gap-3">
                            <Button
                                variant="secondary"
                                icon="fas fa-file-invoice"
                                onClick={() => handleOpenPreview('invoice')}
                            >
                                Invoice Preview
                            </Button>
                            <Button
                                variant="secondary"
                                icon="fas fa-file-invoice-dollar"
                                onClick={() => handleOpenPreview('quotation')}
                            >
                                Quotation Preview
                            </Button>
                            <Button
                                variant="secondary"
                                icon="fas fa-file-contract"
                                onClick={() => handleOpenPreview('contract')}
                            >
                                Contract Preview
                            </Button>
                        </div>
                    </div>

                    {/* A4 Paper Size Section */}
                    <div className="col-span-1 md:col-span-3 bg-gray-50 p-4 rounded-lg">
                        <h3 className="text-md font-medium text-gray-700 mb-3 flex items-center">
                            <i className="fas fa-file-alt mr-2 text-blue-600"></i>
                            A4 Paper Configuration
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Paper Size
                                </label>
                                <select
                                    name="paperSize"
                                    value={templates.paperSize || 'a4'}
                                    onChange={handleTemplateChange}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                >
                                    <option value="a4">A4 (210×297mm) - Recommended</option>
                                    <option value="letter">Letter (8.5×11in)</option>
                                    <option value="legal">Legal (8.5×14in)</option>
                                    <option value="a5">A5 (148×210mm)</option>
                                </select>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Orientation
                                </label>
                                <select
                                    name="orientation"
                                    value={templates.orientation || 'portrait'}
                                    onChange={handleTemplateChange}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                >
                                    <option value="portrait">Portrait (Recommended)</option>
                                    <option value="landscape">Landscape</option>
                                </select>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Margin Presets
                                </label>
                                <select
                                    onChange={(e) => handlePresetMargins(e.target.value)}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    defaultValue=""
                                    data-margin-preset="true"
                                >
                                    <option value="">Select Preset...</option>
                                    <option value="a4-minimal">Minimal (10mm all sides)</option>
                                    <option value="a4-standard">Standard (15mm all sides)</option>
                                    <option value="a4-comfortable">Comfortable (20mm all sides)</option>
                                    <option value="a4-wide">Wide Margins (25mm left/right)</option>
                                </select>
                                {currentPreset !== 'custom' && (
                                    <p className="mt-1 text-xs text-green-600">
                                        <i className="fas fa-check-circle mr-1"></i>
                                        Current: {currentPreset === 'a4-minimal' ? 'Minimal' :
                                                 currentPreset === 'a4-standard' ? 'Standard' :
                                                 currentPreset === 'a4-comfortable' ? 'Comfortable' : 'Wide Margins'}
                                    </p>
                                )}
                                {currentPreset === 'custom' && (
                                    <p className="mt-1 text-xs text-blue-600">
                                        <i className="fas fa-edit mr-1"></i>
                                        Custom margins
                                    </p>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Typography Section */}
                    <div className="col-span-1 md:col-span-3 bg-green-50 p-4 rounded-lg">
                        <h3 className="text-md font-medium text-gray-700 mb-3 flex items-center">
                            <i className="fas fa-font mr-2 text-green-600"></i>
                            Typography & Readability
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Font Family
                                </label>
                                <select
                                    name="fontFamily"
                                    value={templates.fontFamily || 'Segoe UI, sans-serif'}
                                    onChange={handleTemplateChange}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                >
                                    <option value="Segoe UI, sans-serif">Segoe UI (Modern)</option>
                                    <option value="Arial, sans-serif">Arial (Classic)</option>
                                    <option value="Helvetica, sans-serif">Helvetica (Professional)</option>
                                    <option value="Times New Roman, serif">Times New Roman (Traditional)</option>
                                    <option value="Georgia, serif">Georgia (Elegant)</option>
                                    <option value="Verdana, sans-serif">Verdana (Clear)</option>
                                    <option value="Tahoma, sans-serif">Tahoma (Compact)</option>
                                    <option value="Calibri, sans-serif">Calibri (Office)</option>
                                </select>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Font Size
                                </label>
                                <select
                                    name="fontSize"
                                    value={templates.fontSize || 'medium'}
                                    onChange={handleTemplateChange}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                >
                                    <option value="small">Small (10pt - Compact)</option>
                                    <option value="medium">Medium (11pt - Standard)</option>
                                    <option value="large">Large (12pt - Readable)</option>
                                    <option value="extra-large">Extra Large (13pt - Accessible)</option>
                                </select>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Line Height
                                </label>
                                <select
                                    name="lineHeight"
                                    value={templates.lineHeight || 'normal'}
                                    onChange={handleTemplateChange}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                >
                                    <option value="tight">Tight (1.2)</option>
                                    <option value="normal">Normal (1.4)</option>
                                    <option value="relaxed">Relaxed (1.6)</option>
                                    <option value="loose">Loose (1.8)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    {/* Visual Design Section */}
                    <div className="col-span-1 md:col-span-3 bg-purple-50 p-4 rounded-lg">
                        <h3 className="text-md font-medium text-gray-700 mb-3 flex items-center">
                            <i className="fas fa-palette mr-2 text-purple-600"></i>
                            Visual Design & Branding
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Header Color
                                </label>
                                <div className="flex items-center space-x-2">
                                    <input
                                        type="color"
                                        name="headerColor"
                                        value={templates.headerColor || '#3b82f6'}
                                        onChange={handleTemplateChange}
                                        className="h-8 w-8 border border-gray-300 rounded p-0"
                                    />
                                    <input
                                        type="text"
                                        name="headerColor"
                                        value={templates.headerColor || '#3b82f6'}
                                        onChange={handleTemplateChange}
                                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    />
                                </div>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Accent Color
                                </label>
                                <div className="flex items-center space-x-2">
                                    <input
                                        type="color"
                                        name="accentColor"
                                        value={templates.accentColor || '#1e3a8a'}
                                        onChange={handleTemplateChange}
                                        className="h-8 w-8 border border-gray-300 rounded p-0"
                                    />
                                    <input
                                        type="text"
                                        name="accentColor"
                                        value={templates.accentColor || '#1e3a8a'}
                                        onChange={handleTemplateChange}
                                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    />
                                </div>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Header Style
                                </label>
                                <select
                                    name="headerStyle"
                                    value={templates.headerStyle || 'modern'}
                                    onChange={handleTemplateChange}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                >
                                    <option value="modern">Modern (Clean)</option>
                                    <option value="classic">Classic (Traditional)</option>
                                    <option value="minimal">Minimal (Simple)</option>
                                    <option value="bold">Bold (Strong)</option>
                                </select>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Border Style
                                </label>
                                <select
                                    name="borderStyle"
                                    value={templates.borderStyle || 'minimal'}
                                    onChange={handleTemplateChange}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                >
                                    <option value="none">No Borders</option>
                                    <option value="minimal">Minimal</option>
                                    <option value="standard">Standard</option>
                                    <option value="bold">Bold</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    {/* Margins Section */}
                    <div className="col-span-1 md:col-span-3 bg-orange-50 p-4 rounded-lg">
                        <h3 className="text-md font-medium text-gray-700 mb-3 flex items-center">
                            <i className="fas fa-expand-arrows-alt mr-2 text-orange-600"></i>
                            Margins & Spacing (mm)
                        </h3>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div>
                                <label className="block text-sm text-gray-700 mb-1">
                                    Top
                                </label>
                                <input
                                    type="number"
                                    name="top"
                                    value={margins.top || 15}
                                    onChange={handleMarginChange}
                                    min="5"
                                    max="50"
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                />
                                <div className="text-xs text-gray-500 mt-1">Min: 5mm</div>
                            </div>
                            <div>
                                <label className="block text-sm text-gray-700 mb-1">
                                    Right
                                </label>
                                <input
                                    type="number"
                                    name="right"
                                    value={margins.right || 15}
                                    onChange={handleMarginChange}
                                    min="5"
                                    max="50"
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                />
                                <div className="text-xs text-gray-500 mt-1">Min: 5mm</div>
                            </div>
                            <div>
                                <label className="block text-sm text-gray-700 mb-1">
                                    Bottom
                                </label>
                                <input
                                    type="number"
                                    name="bottom"
                                    value={margins.bottom || 15}
                                    onChange={handleMarginChange}
                                    min="5"
                                    max="50"
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                />
                                <div className="text-xs text-gray-500 mt-1">Min: 5mm</div>
                            </div>
                            <div>
                                <label className="block text-sm text-gray-700 mb-1">
                                    Left
                                </label>
                                <input
                                    type="number"
                                    name="left"
                                    value={margins.left || 15}
                                    onChange={handleMarginChange}
                                    min="5"
                                    max="50"
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                />
                                <div className="text-xs text-gray-500 mt-1">Min: 5mm</div>
                            </div>
                        </div>
                        <div className="mt-3 text-sm text-gray-600 bg-white p-2 rounded border-l-4 border-orange-400">
                            <i className="fas fa-lightbulb mr-1"></i>
                            <strong>Tip:</strong> For professional printing, use at least 10mm margins. Standard office printers typically require 5-6mm minimum.
                        </div>
                    </div>

                    {/* Layout & Content Section */}
                    <div className="col-span-1 md:col-span-3 bg-indigo-50 p-4 rounded-lg">
                        <h3 className="text-md font-medium text-gray-700 mb-3 flex items-center">
                            <i className="fas fa-layout mr-2 text-indigo-600"></i>
                            Layout & Content Options
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Logo Position
                                </label>
                                <select
                                    name="logoPosition"
                                    value={templates.logoPosition || 'top-right'}
                                    onChange={handleTemplateChange}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                >
                                    <option value="top-left">Top Left</option>
                                    <option value="top-right">Top Right</option>
                                    <option value="top-center">Top Center</option>
                                    <option value="header-left">Header Left</option>
                                </select>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Logo Size
                                </label>
                                <select
                                    name="logoSize"
                                    value={templates.logoSize || 'medium'}
                                    onChange={handleTemplateChange}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                >
                                    <option value="small">Small (40px)</option>
                                    <option value="medium">Medium (60px)</option>
                                    <option value="large">Large (80px)</option>
                                    <option value="extra-large">Extra Large (100px)</option>
                                </select>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Table Style
                                </label>
                                <select
                                    name="tableStyle"
                                    value={templates.tableStyle || 'striped'}
                                    onChange={handleTemplateChange}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                >
                                    <option value="simple">Simple</option>
                                    <option value="striped">Striped Rows</option>
                                    <option value="bordered">Bordered</option>
                                    <option value="modern">Modern</option>
                                </select>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Footer Text
                                </label>
                                <input
                                    type="text"
                                    name="footerText"
                                    value={templates.footerText || ''}
                                    onChange={handleTemplateChange}
                                    placeholder="e.g., Thank you for your business"
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Display Options Section */}
                    <div className="col-span-1 md:col-span-3 bg-teal-50 p-4 rounded-lg">
                        <h3 className="text-md font-medium text-gray-700 mb-3 flex items-center">
                            <i className="fas fa-eye mr-2 text-teal-600"></i>
                            Display & Content Options
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-4">
                                <h4 className="text-sm font-medium text-gray-600 mb-2">Visual Elements</h4>
                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        name="showLogo"
                                        id="showLogo"
                                        checked={templates.showLogo !== false}
                                        onChange={handleTemplateChange}
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    />
                                    <label htmlFor="showLogo" className="ml-2 block text-sm text-gray-900">
                                        Show Company Logo
                                    </label>
                                </div>
                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        name="showSignature"
                                        id="showSignature"
                                        checked={templates.showSignature !== false}
                                        onChange={handleTemplateChange}
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    />
                                    <label htmlFor="showSignature" className="ml-2 block text-sm text-gray-900">
                                        Show Digital Signature
                                    </label>
                                </div>
                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        name="showWatermark"
                                        id="showWatermark"
                                        checked={templates.showWatermark !== false}
                                        onChange={handleTemplateChange}
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    />
                                    <label htmlFor="showWatermark" className="ml-2 block text-sm text-gray-900">
                                        Show Status Watermark (DRAFT, PAID, etc.)
                                    </label>
                                </div>
                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        name="showPageNumbers"
                                        id="showPageNumbers"
                                        checked={templates.showPageNumbers !== false}
                                        onChange={handleTemplateChange}
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    />
                                    <label htmlFor="showPageNumbers" className="ml-2 block text-sm text-gray-900">
                                        Show Page Numbers
                                    </label>
                                </div>
                            </div>

                            <div className="space-y-4">
                                <h4 className="text-sm font-medium text-gray-600 mb-2">Content Sections</h4>
                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        name="showCompanyDetails"
                                        id="showCompanyDetails"
                                        checked={templates.showCompanyDetails !== false}
                                        onChange={handleTemplateChange}
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    />
                                    <label htmlFor="showCompanyDetails" className="ml-2 block text-sm text-gray-900">
                                        Show Company Details
                                    </label>
                                </div>
                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        name="showBankDetails"
                                        id="showBankDetails"
                                        checked={templates.showBankDetails !== false}
                                        onChange={handleTemplateChange}
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    />
                                    <label htmlFor="showBankDetails" className="ml-2 block text-sm text-gray-900">
                                        Show Bank Details
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div className="mt-4 p-3 bg-white rounded border-l-4 border-teal-400">
                            <div className="text-sm text-gray-600">
                                <i className="fas fa-info-circle mr-1 text-teal-600"></i>
                                <strong>A4 Optimization:</strong> These settings are optimized for A4 paper size (210×297mm) to ensure professional appearance when printed or exported to PDF.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('TemplateSettings component error:', error);
        reportError(error);
        return null;
    }
}
