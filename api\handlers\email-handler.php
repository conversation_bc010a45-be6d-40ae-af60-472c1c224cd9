<?php
/**
 * <PERSON>ail Handler
 * Handles email configuration testing and management
 */

require_once __DIR__ . '/../utils/EmailUtils.php';
require_once __DIR__ . '/../utils/PHPMailerSMTP.php';

function handleEmail($action = null) {
    global $conn;
    
    // Get request method
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'POST':
            if ($action === 'test-email') {
                return handleTestEmail();
            }
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
}

function handleTestEmail() {
    try {
        // Get request body
        $requestBody = file_get_contents('php://input');
        $data = json_decode($requestBody, true);
        
        if (!$data || !isset($data['emailSettings']) || !isset($data['testEmail'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Email settings and test email are required']);
            return;
        }
        
        $emailSettings = $data['emailSettings'];
        $testEmail = $data['testEmail'];
        
        // Validate required fields
        $requiredFields = ['smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'from_email'];
        foreach ($requiredFields as $field) {
            if (empty($emailSettings[$field])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => "Missing required field: $field"]);
                return;
            }
        }
        
        // Validate email format
        if (!filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid test email address']);
            return;
        }
        
        // Check if user wants to force real email sending
        $forceRealEmail = isset($data['forceRealEmail']) ? $data['forceRealEmail'] : false;

        // Test email configuration
        $result = testEmailConfiguration($emailSettings, $testEmail, $forceRealEmail);

        echo json_encode($result);
        
    } catch (Exception $e) {
        error_log("Test email error: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Failed to test email configuration']);
    }
}

function testEmailConfiguration($emailSettings, $testEmail, $forceRealEmail = false) {
    try {
        // For development mode, just log and return success (unless forced)
        if (isDevelopmentMode() && !$forceRealEmail) {
            $message = "
            <html>
            <head>
                <title>Email Configuration Test</title>
            </head>
            <body>
                <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
                    <h2 style='color: #10b981;'>✅ Email Configuration Test</h2>
                    
                    <p>Congratulations! Your email configuration is working correctly.</p>
                    
                    <div style='background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 20px 0;'>
                        <h3 style='margin: 0 0 10px 0; color: #374151;'>Configuration Details:</h3>
                        <ul style='margin: 0; padding-left: 20px; color: #6b7280;'>
                            <li><strong>SMTP Host:</strong> " . htmlspecialchars($emailSettings['smtp_host']) . "</li>
                            <li><strong>SMTP Port:</strong> " . htmlspecialchars($emailSettings['smtp_port']) . "</li>
                            <li><strong>Username:</strong> " . htmlspecialchars($emailSettings['smtp_username']) . "</li>
                            <li><strong>Encryption:</strong> " . htmlspecialchars($emailSettings['smtp_encryption']) . "</li>
                            <li><strong>From Email:</strong> " . htmlspecialchars($emailSettings['from_email']) . "</li>
                        </ul>
                    </div>
                    
                    <p>This test email was sent successfully to verify your SMTP settings.</p>
                    
                    <hr style='margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;'>
                    
                    <p style='color: #6b7280; font-size: 14px;'>
                        Best regards,<br>
                        The Bizma Team
                    </p>
                </div>
            </body>
            </html>
            ";
            
            error_log("=== TEST EMAIL WOULD BE SENT ===");
            error_log("To: " . $testEmail);
            error_log("Subject: Email Configuration Test - Bizma");
            error_log("SMTP Host: " . $emailSettings['smtp_host']);
            error_log("SMTP Port: " . $emailSettings['smtp_port']);
            error_log("SMTP Username: " . $emailSettings['smtp_username']);
            error_log("From Email: " . $emailSettings['from_email']);
            error_log("Message: " . strip_tags($message));
            error_log("=== END TEST EMAIL ===");
            
            return [
                'success' => true,
                'message' => 'Test email sent successfully! (Development mode: Check error logs for email content)'
            ];
        }

        // Production email sending - use SMTP mailer
        return SimpleSMTPMailer::sendEmail($emailSettings, $testEmail, 'Email Configuration Test - Bizma', $message);
        
    } catch (Exception $e) {
        error_log("Email test failed: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Failed to send test email: ' . $e->getMessage()
        ];
    }
}

function isDevelopmentMode() {
    return (
        $_SERVER['HTTP_HOST'] === 'localhost' ||
        strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false ||
        strpos($_SERVER['HTTP_HOST'], 'dev.') === 0 ||
        strpos($_SERVER['HTTP_HOST'], 'test.') === 0
    );
}



/**
 * Save email settings to database
 */
function saveEmailSettings($emailSettings, $companyId) {
    global $conn;
    
    try {
        // Encrypt sensitive data before storing
        $encryptedPassword = base64_encode($emailSettings['smtp_password']);
        
        $settingsJson = json_encode([
            'smtp_host' => $emailSettings['smtp_host'],
            'smtp_port' => $emailSettings['smtp_port'],
            'smtp_username' => $emailSettings['smtp_username'],
            'smtp_password' => $encryptedPassword,
            'smtp_encryption' => $emailSettings['smtp_encryption'],
            'from_email' => $emailSettings['from_email'],
            'from_name' => $emailSettings['from_name'],
            'reply_to' => $emailSettings['reply_to'],
            'enabled' => $emailSettings['enabled']
        ]);
        
        // Check if email settings already exist
        $sql = "SELECT object_id FROM company_settings WHERE company_id = ? AND setting_key = 'email_settings'";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $companyId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            // Update existing settings
            $sql = "UPDATE company_settings SET setting_value = ?, updated_at = NOW() WHERE company_id = ? AND setting_key = 'email_settings'";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("ss", $settingsJson, $companyId);
        } else {
            // Insert new settings
            $settingId = 'setting_' . time() . '_' . rand(100, 999);
            $sql = "INSERT INTO company_settings (object_id, company_id, setting_key, setting_value, created_at, updated_at) VALUES (?, ?, 'email_settings', ?, NOW(), NOW())";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("sss", $settingId, $companyId, $settingsJson);
        }
        
        return $stmt->execute();
        
    } catch (Exception $e) {
        error_log("Failed to save email settings: " . $e->getMessage());
        return false;
    }
}

/**
 * Load email settings from database
 */
function loadEmailSettings($companyId) {
    global $conn;
    
    try {
        $sql = "SELECT setting_value FROM company_settings WHERE company_id = ? AND setting_key = 'email_settings'";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $companyId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $settings = json_decode($row['setting_value'], true);
            
            // Decrypt password
            if (isset($settings['smtp_password'])) {
                $settings['smtp_password'] = base64_decode($settings['smtp_password']);
            }
            
            return $settings;
        }
        
        return null;
        
    } catch (Exception $e) {
        error_log("Failed to load email settings: " . $e->getMessage());
        return null;
    }
}

?>
