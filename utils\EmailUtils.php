<?php
/**
 * Email Utilities
 * Provides email-related functions for the application
 */

class EmailUtils {
    
    /**
     * Send verification email
     * 
     * @param string $email The recipient's email address
     * @param string $name The recipient's name
     * @param string $verificationLink The verification link
     * @return array Status array with success flag and message
     */
    public static function sendVerificationEmail($email, $name, $verificationLink) {
        $subject = "Verify your Bizma account";
        
        $message = "
        <html>
        <head>
            <title>Verify your Bizma account</title>
        </head>
        <body>
            <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
                <h2 style='color: #2563eb;'>Welcome to Bizma!</h2>
                <p>Hi {$name},</p>
                <p>Thank you for registering with Bizma. To complete your registration, please verify your email address by clicking the button below:</p>
                <div style='text-align: center; margin: 30px 0;'>
                    <a href='{$verificationLink}' style='background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;'>Verify Email Address</a>
                </div>
                <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                <p><a href='{$verificationLink}'>{$verificationLink}</a></p>
                <p>This verification link will expire in 24 hours.</p>
                <hr style='margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;'>
                <p style='color: #6b7280; font-size: 14px;'>
                    If you didn't create an account with Bizma, you can safely ignore this email.
                </p>
                <p style='color: #6b7280; font-size: 14px;'>
                    Best regards,<br>
                    The Bizma Team
                </p>
            </div>
        </body>
        </html>
        ";
        
        $headers = [
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: Bizma <<EMAIL>>',
            'Reply-To: <EMAIL>',
            'X-Mailer: PHP/' . phpversion()
        ];
        
        // Suppress warnings with @ operator to prevent mail server errors from breaking the app
        $mailSent = @mail($email, $subject, $message, implode("\r\n", $headers));
        
        if ($mailSent) {
            return [
                'success' => true,
                'message' => 'Verification email sent successfully'
            ];
        } else {
            // Log the error
            error_log("Failed to send verification email to {$email}. Check mail server configuration.");
            
            return [
                'success' => false,
                'message' => 'Failed to send verification email. Please check your mail server configuration.',
                'debug_info' => error_get_last()
            ];
        }
    }
    
    /**
     * Send password reset email
     * 
     * @param string $email The recipient's email address
     * @param string $name The recipient's name
     * @param string $resetLink The password reset link
     * @return array Status array with success flag and message
     */
    public static function sendPasswordResetEmail($email, $name, $resetLink) {
        $subject = "Reset your Bizma password";
        
        $message = "
        <html>
        <head>
            <title>Reset your Bizma password</title>
        </head>
        <body>
            <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
                <h2 style='color: #2563eb;'>Password Reset Request</h2>
                <p>Hi {$name},</p>
                <p>We received a request to reset your password for your Bizma account. Click the button below to reset your password:</p>
                <div style='text-align: center; margin: 30px 0;'>
                    <a href='{$resetLink}' style='background-color: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;'>Reset Password</a>
                </div>
                <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                <p><a href='{$resetLink}'>{$resetLink}</a></p>
                <p>This password reset link will expire in 1 hour.</p>
                <hr style='margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;'>
                <p style='color: #6b7280; font-size: 14px;'>
                    If you didn't request a password reset, you can safely ignore this email. Your password will not be changed.
                </p>
                <p style='color: #6b7280; font-size: 14px;'>
                    Best regards,<br>
                    The Bizma Team
                </p>
            </div>
        </body>
        </html>
        ";
        
        $headers = [
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: Bizma <<EMAIL>>',
            'Reply-To: <EMAIL>',
            'X-Mailer: PHP/' . phpversion()
        ];
        
        // Suppress warnings with @ operator to prevent mail server errors from breaking the app
        $mailSent = @mail($email, $subject, $message, implode("\r\n", $headers));
        
        if ($mailSent) {
            return [
                'success' => true,
                'message' => 'Password reset email sent successfully'
            ];
        } else {
            // Log the error
            error_log("Failed to send password reset email to {$email}. Check mail server configuration.");
            
            return [
                'success' => false,
                'message' => 'Failed to send password reset email. Please check your mail server configuration.',
                'debug_info' => error_get_last()
            ];
        }
    }
    
    /**
     * Send welcome email
     * 
     * @param string $email The recipient's email address
     * @param string $name The recipient's name
     * @param string $companyName The company name
     * @return array Status array with success flag and message
     */
    public static function sendWelcomeEmail($email, $name, $companyName) {
        $subject = "Welcome to Bizma - Let's get started!";
        
        $message = "
        <html>
        <head>
            <title>Welcome to Bizma</title>
        </head>
        <body>
            <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
                <h2 style='color: #2563eb;'>Welcome to Bizma!</h2>
                <p>Hi {$name},</p>
                <p>Congratulations! Your Bizma account for <strong>{$companyName}</strong> has been successfully created.</p>
                <p>You can now start managing your business with our comprehensive platform that includes:</p>
                <ul style='color: #374151;'>
                    <li>Customer Management</li>
                    <li>Invoice Generation</li>
                    <li>Quotation Management</li>
                    <li>Contract Management</li>
                    <li>Lead Tracking</li>
                    <li>Business Analytics</li>
                    <li>And much more!</li>
                </ul>
                <div style='text-align: center; margin: 30px 0;'>
                    <a href='http://localhost/biz/dashboard' style='background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;'>Get Started</a>
                </div>
                <p>If you have any questions or need help getting started, don't hesitate to reach out to our support <NAME_EMAIL>.</p>
                <hr style='margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;'>
                <p style='color: #6b7280; font-size: 14px;'>
                    Best regards,<br>
                    The Bizma Team
                </p>
            </div>
        </body>
        </html>
        ";
        
        $headers = [
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: Bizma <<EMAIL>>',
            'Reply-To: <EMAIL>',
            'X-Mailer: PHP/' . phpversion()
        ];
        
        // Suppress warnings with @ operator to prevent mail server errors from breaking the app
        $mailSent = @mail($email, $subject, $message, implode("\r\n", $headers));
        
        if ($mailSent) {
            return [
                'success' => true,
                'message' => 'Welcome email sent successfully'
            ];
        } else {
            // Log the error
            error_log("Failed to send welcome email to {$email}. Check mail server configuration.");
            
            return [
                'success' => false,
                'message' => 'Failed to send welcome email. Please check your mail server configuration.',
                'debug_info' => error_get_last()
            ];
        }
    }
    
    /**
     * Send notification email
     * 
     * @param string $email The recipient's email address
     * @param string $subject The email subject
     * @param string $message The email message (HTML)
     * @param string $fromName The sender's name
     * @return array Status array with success flag and message
     */
    public static function sendNotificationEmail($email, $subject, $message, $fromName = 'Bizma') {
        $headers = [
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: ' . $fromName . ' <<EMAIL>>',
            'Reply-To: <EMAIL>',
            'X-Mailer: PHP/' . phpversion()
        ];
        
        // Suppress warnings with @ operator to prevent mail server errors from breaking the app
        $mailSent = @mail($email, $subject, $message, implode("\r\n", $headers));
        
        if ($mailSent) {
            return [
                'success' => true,
                'message' => 'Notification email sent successfully'
            ];
        } else {
            // Log the error
            error_log("Failed to send notification email to {$email}. Check mail server configuration.");
            
            return [
                'success' => false,
                'message' => 'Failed to send notification email. Please check your mail server configuration.',
                'debug_info' => error_get_last()
            ];
        }
    }
    
    /**
     * Validate email configuration
     * 
     * @return array Status array with valid flag and error message if invalid
     */
    public static function validateEmailConfig() {
        // Check if mail function is available
        if (!function_exists('mail')) {
            return ['valid' => false, 'error' => 'Mail function not available'];
        }
        
        // Check if SMTP settings are configured in php.ini
        $smtpHost = ini_get('SMTP');
        $smtpPort = ini_get('smtp_port');
        
        if (empty($smtpHost) || $smtpHost === 'localhost') {
            return [
                'valid' => false, 
                'error' => 'SMTP server not configured properly in php.ini',
                'details' => [
                    'smtp_host' => $smtpHost,
                    'smtp_port' => $smtpPort
                ]
            ];
        }
        
        return ['valid' => true];
    }
    
    /**
     * Test email sending
     * 
     * @param string $email The recipient's email address for testing
     * @return array Status array with success flag and message
     */
    public static function testEmail($email) {
        $subject = "Bizma Email Test";
        $message = "This is a test email from Bizma. If you receive this, email configuration is working correctly.";
        
        $headers = [
            'From: Bizma <<EMAIL>>',
            'Reply-To: <EMAIL>',
            'X-Mailer: PHP/' . phpversion()
        ];
        
        // Suppress warnings with @ operator to prevent mail server errors from breaking the app
        $mailSent = @mail($email, $subject, $message, implode("\r\n", $headers));
        
        if ($mailSent) {
            return [
                'success' => true,
                'message' => 'Test email sent successfully'
            ];
        } else {
            // Get error details
            $error = error_get_last();
            
            // Check PHP mail configuration
            $smtpHost = ini_get('SMTP');
            $smtpPort = ini_get('smtp_port');
            
            return [
                'success' => false,
                'message' => 'Failed to send test email. Mail server configuration issue.',
                'debug_info' => [
                    'error' => $error,
                    'smtp_host' => $smtpHost,
                    'smtp_port' => $smtpPort
                ]
            ];
        }
    }
    
    /**
     * Get mail configuration status
     * 
     * @return array Mail configuration status
     */
    public static function getMailConfigStatus() {
        return [
            'mail_function_available' => function_exists('mail'),
            'smtp_host' => ini_get('SMTP'),
            'smtp_port' => ini_get('smtp_port'),
            'sendmail_path' => ini_get('sendmail_path'),
            'php_version' => phpversion()
        ];
    }
}
?>
