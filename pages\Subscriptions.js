function Subscriptions() {
    try {
        const authContext = React.useContext(AuthContext);
        const [subscriptions, setSubscriptions] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [selectedPlan, setSelectedPlan] = React.useState(null);
        const [showUpgradeModal, setShowUpgradeModal] = React.useState(false);
        const [notification, setNotification] = React.useState(null);

        const plans = [
            {
                id: 'basic',
                name: 'Basic',
                price: 999,
                billingPeriod: 'monthly',
                features: [
                    'Up to 100 leads',
                    '3 team members',
                    'Basic reporting',
                    'Email support',
                    '5GB storage'
                ],
                maxLeads: 100,
                maxUsers: 3,
                color: 'blue'
            },
            {
                id: 'professional',
                name: 'Professional',
                price: 2499,
                billingPeriod: 'monthly',
                features: [
                    'Up to 1,000 leads',
                    '10 team members',
                    'Advanced reporting',
                    'Priority support',
                    '25GB storage',
                    'API access',
                    'Custom fields'
                ],
                maxLeads: 1000,
                maxUsers: 10,
                color: 'purple',
                popular: true
            },
            {
                id: 'enterprise',
                name: 'Enterprise',
                price: 4999,
                billingPeriod: 'monthly',
                features: [
                    'Unlimited leads',
                    'Unlimited team members',
                    'Custom reporting',
                    '24/7 phone support',
                    'Unlimited storage',
                    'API access',
                    'Custom fields',
                    'Dedicated account manager',
                    'Custom integrations',
                    'SLA guarantee'
                ],
                maxLeads: Infinity,
                maxUsers: Infinity,
                color: 'indigo'
            }
        ];

        React.useEffect(() => {
            fetchSubscriptions();
        }, []);

        const fetchSubscriptions = async () => {
            try {
                setLoading(true);
                const response = await trickleListObjects('subscription', 100, true);
                setSubscriptions(response.items);
            } catch (error) {
                console.error('Error fetching subscriptions:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to load subscription data'
                });
            } finally {
                setLoading(false);
            }
        };

        const handleUpgrade = (plan) => {
            setSelectedPlan(plan);
            setShowUpgradeModal(true);
        };

        const handleConfirmUpgrade = async () => {
            try {
                console.log('=== Starting subscription upgrade process ===');
                setLoading(true);
                const { user, currentCompany } = authContext;

                console.log('Selected plan:', selectedPlan);
                console.log('Current company:', currentCompany);
                console.log('User:', user);

                if (!selectedPlan) {
                    throw new Error('No plan selected');
                }

                // Use currentCompany if available, otherwise create from user data
                let companyToUse = currentCompany;
                if (!companyToUse || !companyToUse.objectId) {
                    if (user.company_id) {
                        companyToUse = {
                            objectId: user.company_id,
                            objectType: 'company',
                            objectData: {
                                name: user.company_name || 'Default Company'
                            }
                        };
                        console.log('Using company from user data:', companyToUse);
                    } else {
                        throw new Error('No company selected or invalid company');
                    }
                }

                // Get authentication token from localStorage
                const authToken = localStorage.getItem('authToken');
                if (!user || !authToken) {
                    throw new Error('User not authenticated');
                }

                console.log('Auth token found:', authToken ? 'Yes' : 'No');

                // First create the subscription record
                const subscriptionData = {
                    planId: selectedPlan.id,
                    planName: selectedPlan.name,
                    price: selectedPlan.price,
                    billingPeriod: selectedPlan.billingPeriod,
                    status: 'inactive', // Will be activated after payment
                    startDate: new Date().toISOString(),
                    maxLeads: selectedPlan.maxLeads,
                    maxUsers: selectedPlan.maxUsers,
                    features: selectedPlan.features
                };

                console.log('Creating subscription with data:', subscriptionData);

                let subscription;
                try {
                    subscription = await trickleCreateObject('subscription', subscriptionData);
                    console.log('Raw subscription response:', subscription);
                    console.log('Subscription type:', typeof subscription);
                    console.log('Subscription keys:', subscription ? Object.keys(subscription) : 'null/undefined');
                } catch (subscriptionError) {
                    console.error('Subscription creation failed with error:', subscriptionError);
                    throw new Error(`Subscription creation failed: ${subscriptionError.message}`);
                }

                // Validate subscription creation response
                if (!subscription || !subscription.objectId) {
                    console.error('Invalid subscription response:', subscription);
                    throw new Error(`Failed to create subscription - invalid response. Got: ${JSON.stringify(subscription)}`);
                }

                console.log('Subscription created successfully:', subscription);

                // TODO: Temporarily skip payment for testing - payment API has issues
                console.log('Skipping payment for now - subscription created successfully');
                alert(`Subscription created successfully! ID: ${subscription.objectId}\n\nPayment integration will be completed next.`);

                // For now, just refresh the page to show the new subscription
                window.location.reload();

                // COMMENTED OUT - Payment creation (has issues)
                /*
                const paymentResponse = await fetch('/api/payment/payment-api.php/create-subscription-payment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        subscription_id: subscription.objectId,
                        amount: selectedPlan.price,
                        company_id: companyToUse.objectId
                    })
                });

                const paymentResult = await paymentResponse.json();

                if (paymentResult.success) {
                    // Redirect to PhonePe payment page
                    window.location.href = paymentResult.payment_url;
                } else {
                    throw new Error(paymentResult.error || 'Failed to initiate payment');
                }
                */
            } catch (error) {
                console.error('Error upgrading subscription:', error);
                setNotification({
                    type: 'error',
                    message: error.message || 'Failed to upgrade subscription'
                });
                setLoading(false);
            }
        };

        const getCurrentPlan = () => {
            if (subscriptions.length === 0) return null;
            return subscriptions[0].objectData;
        };

        const renderPlanCard = (plan) => {
            const currentPlan = getCurrentPlan();
            const isCurrentPlan = currentPlan && currentPlan.planId === plan.id;

            return (
                <div
                    key={plan.id}
                    className={`bg-white rounded-lg shadow-lg overflow-hidden border-2 ${
                        plan.popular ? 'border-blue-500 transform scale-105' : 'border-transparent'
                    }`}
                >
                    {plan.popular && (
                        <div className="bg-blue-500 text-white text-center text-sm py-1">
                            Most Popular
                        </div>
                    )}
                    <div className="p-6">
                        <h3 className="text-2xl font-bold text-gray-900">{plan.name}</h3>
                        <div className="mt-4">
                            <span className="text-4xl font-bold">₹{plan.price}</span>
                            <span className="text-gray-500">/{plan.billingPeriod}</span>
                        </div>
                        <ul className="mt-6 space-y-4">
                            {plan.features.map((feature, index) => (
                                <li key={index} className="flex items-center">
                                    <i className="fas fa-check text-green-500 mr-2"></i>
                                    <span className="text-gray-600">{feature}</span>
                                </li>
                            ))}
                        </ul>
                        <div className="mt-8">
                            <Button
                                onClick={() => handleUpgrade(plan)}
                                disabled={isCurrentPlan}
                                className="w-full"
                            >
                                {isCurrentPlan ? 'Current Plan' : 'Upgrade'}
                            </Button>
                        </div>
                    </div>
                </div>
            );
        };

        return (
            <div data-name="subscriptions-page">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h1 className="text-3xl font-bold text-gray-900 sm:text-4xl">
                            Choose Your Plan
                        </h1>
                        <p className="mt-4 text-xl text-gray-600">
                            Select the perfect plan for your business needs
                        </p>
                    </div>

                    {notification && (
                        <Notification
                            type={notification.type}
                            message={notification.message}
                            onClose={() => setNotification(null)}
                        />
                    )}

                    {loading ? (
                        <div className="flex justify-center items-center h-64">
                            <i className="fas fa-spinner fa-spin fa-2x text-blue-500"></i>
                        </div>
                    ) : (
                        <div className="mt-12 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                            {plans.map(renderPlanCard)}
                        </div>
                    )}

                    {/* Current Subscription Details */}
                    {getCurrentPlan() && (
                        <div className="mt-12 bg-white rounded-lg shadow p-6">
                            <h2 className="text-2xl font-bold mb-4">Current Subscription</h2>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                <div>
                                    <h3 className="text-sm font-medium text-gray-500">Plan</h3>
                                    <p className="mt-1 text-lg font-semibold">
                                        {getCurrentPlan().planName}
                                    </p>
                                </div>
                                <div>
                                    <h3 className="text-sm font-medium text-gray-500">Status</h3>
                                    <p className="mt-1">
                                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            {getCurrentPlan().status}
                                        </span>
                                    </p>
                                </div>
                                <div>
                                    <h3 className="text-sm font-medium text-gray-500">Start Date</h3>
                                    <p className="mt-1 text-lg font-semibold">
                                        {formatDate(getCurrentPlan().startDate)}
                                    </p>
                                </div>
                                <div>
                                    <h3 className="text-sm font-medium text-gray-500">Price</h3>
                                    <p className="mt-1 text-lg font-semibold">
                                        ₹{getCurrentPlan().price}/{getCurrentPlan().billingPeriod}
                                    </p>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Usage Stats */}
                    {getCurrentPlan() && (
                        <div className="mt-8 bg-white rounded-lg shadow p-6">
                            <h2 className="text-2xl font-bold mb-4">Usage Statistics</h2>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <h3 className="text-sm font-medium text-gray-500">Leads Used</h3>
                                    <div className="mt-2">
                                        <div className="flex items-center justify-between mb-1">
                                            <span className="text-sm font-medium text-gray-700">
                                                50/{getCurrentPlan().maxLeads === Infinity ? 'Unlimited' : getCurrentPlan().maxLeads}
                                            </span>
                                            <span className="text-sm font-medium text-gray-500">
                                                {getCurrentPlan().maxLeads === Infinity ? '∞' : `${Math.round((50 / getCurrentPlan().maxLeads) * 100)}%`}
                                            </span>
                                        </div>
                                        <div className="w-full bg-gray-200 rounded-full h-2">
                                            <div
                                                className="bg-blue-600 h-2 rounded-full"
                                                style={{ width: getCurrentPlan().maxLeads === Infinity ? '10%' : `${(50 / getCurrentPlan().maxLeads) * 100}%` }}
                                            ></div>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <h3 className="text-sm font-medium text-gray-500">Team Members</h3>
                                    <div className="mt-2">
                                        <div className="flex items-center justify-between mb-1">
                                            <span className="text-sm font-medium text-gray-700">
                                                2/{getCurrentPlan().maxUsers === Infinity ? 'Unlimited' : getCurrentPlan().maxUsers}
                                            </span>
                                            <span className="text-sm font-medium text-gray-500">
                                                {getCurrentPlan().maxUsers === Infinity ? '∞' : `${Math.round((2 / getCurrentPlan().maxUsers) * 100)}%`}
                                            </span>
                                        </div>
                                        <div className="w-full bg-gray-200 rounded-full h-2">
                                            <div
                                                className="bg-green-600 h-2 rounded-full"
                                                style={{ width: getCurrentPlan().maxUsers === Infinity ? '10%' : `${(2 / getCurrentPlan().maxUsers) * 100}%` }}
                                            ></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                {/* Upgrade Modal */}
                {showUpgradeModal && selectedPlan && (
                    <Modal
                        isOpen={showUpgradeModal}
                        onClose={() => setShowUpgradeModal(false)}
                        title="Confirm Subscription Upgrade"
                    >
                        <div className="p-6">
                            <p className="text-lg mb-4">
                                Are you sure you want to upgrade to the {selectedPlan.name} plan?
                            </p>
                            <p className="text-gray-600 mb-6">
                                You will be charged ₹{selectedPlan.price} per {selectedPlan.billingPeriod}.
                            </p>
                            <div className="flex justify-end space-x-4">
                                <Button
                                    variant="secondary"
                                    onClick={() => setShowUpgradeModal(false)}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    onClick={handleConfirmUpgrade}
                                    loading={loading}
                                >
                                    Confirm Upgrade
                                </Button>
                            </div>
                        </div>
                    </Modal>
                )}
            </div>
        );
    } catch (error) {
        console.error('Subscriptions page error:', error);
        // Report error to error handler if available
        if (window.ErrorHandler && window.ErrorHandler.handleError) {
            window.ErrorHandler.handleError(error);
        }
        return null;
    }
}
