function Login() {
    try {
        const { login } = useAuth();
        const [formData, setFormData] = React.useState({
            email: '',
            password: '',
            rememberMe: false
        });
        const [loading, setLoading] = React.useState(false);
        const [error, setError] = React.useState('');
        const [showPassword, setShowPassword] = React.useState(false);
        const [loginAttempts, setLoginAttempts] = React.useState(0);
        const [isBlocked, setIsBlocked] = React.useState(false);
        const [blockTimeRemaining, setBlockTimeRemaining] = React.useState(0);

        // Check if user is temporarily blocked
        React.useEffect(() => {
            const blockData = localStorage.getItem('loginBlock');
            if (blockData) {
                const { timestamp, attempts } = JSON.parse(blockData);
                const timeDiff = Date.now() - timestamp;
                const blockDuration = 15 * 60 * 1000; // 15 minutes
                
                if (timeDiff < blockDuration && attempts >= 5) {
                    setIsBlocked(true);
                    setBlockTimeRemaining(Math.ceil((blockDuration - timeDiff) / 1000));
                    
                    const timer = setInterval(() => {
                        setBlockTimeRemaining(prev => {
                            if (prev <= 1) {
                                setIsBlocked(false);
                                localStorage.removeItem('loginBlock');
                                clearInterval(timer);
                                return 0;
                            }
                            return prev - 1;
                        });
                    }, 1000);
                    
                    return () => clearInterval(timer);
                } else if (timeDiff >= blockDuration) {
                    localStorage.removeItem('loginBlock');
                }
            }
        }, []);

        const handleSubmit = async (e) => {
            e.preventDefault();
            
            if (isBlocked) {
                setError(`Too many failed attempts. Please wait ${Math.ceil(blockTimeRemaining / 60)} minutes.`);
                return;
            }
            
            if (!validateForm()) return;

            try {
                setLoading(true);
                setError('');
                
                const result = await login(formData.email, formData.password, formData.rememberMe);
                
                if (result.success) {
                    // Clear any login blocks on successful login
                    localStorage.removeItem('loginBlock');

                    // Redirect based on user role using SPA routing
                    const redirectPath = result.user && result.user.role === 'super_admin' ? '/super-admin' : '/dashboard';
                    const redirectUrl = window.getAppUrl ? window.getAppUrl(redirectPath) : redirectPath;

                    // Use SPA routing instead of full page reload
                    window.history.pushState({}, '', redirectUrl);
                    window.dispatchEvent(new PopStateEvent('popstate'));
                } else {
                    throw new Error(result.message || 'Login failed');
                }
            } catch (error) {
                console.error('Login error:', error);
                
                // Track failed attempts
                const newAttempts = loginAttempts + 1;
                setLoginAttempts(newAttempts);
                
                // Block after 5 failed attempts
                if (newAttempts >= 5) {
                    const blockData = {
                        timestamp: Date.now(),
                        attempts: newAttempts
                    };
                    localStorage.setItem('loginBlock', JSON.stringify(blockData));
                    setIsBlocked(true);
                    setBlockTimeRemaining(15 * 60); // 15 minutes
                    setError('Too many failed attempts. Account temporarily blocked for 15 minutes.');
                } else {
                    setError(error.message || 'Invalid email or password');
                }
            } finally {
                setLoading(false);
            }
        };

        const validateForm = () => {
            if (!formData.email || !formData.password) {
                setError('Please fill in all fields');
                return false;
            }
            if (!isEmailValid(formData.email)) {
                setError('Please enter a valid email address');
                return false;
            }
            if (formData.password.length < 6) {
                setError('Password must be at least 6 characters long');
                return false;
            }
            return true;
        };

        const handleInputChange = (e) => {
            const { name, value, type, checked } = e.target;
            setFormData(prev => ({
                ...prev,
                [name]: type === 'checkbox' ? checked : value
            }));
            setError('');
        };

        return (
            <div data-name="login-page" className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50">
                <div className="sm:mx-auto sm:w-full sm:max-w-md">
                    <img
                        src="https://via.placeholder.com/100x100"
                        alt="Logo"
                        className="mx-auto h-12 w-auto"
                    />
                    <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                        Sign in to your account
                    </h2>
                    <p className="mt-2 text-center text-sm text-gray-600">
                        Or{' '}
                        <a href="/register" className="font-medium text-blue-600 hover:text-blue-500">
                            start your 14-day free trial
                        </a>
                    </p>
                </div>

                <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
                    <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                        <form className="space-y-6" onSubmit={handleSubmit}>
                            <div>
                                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                                    Email address
                                </label>
                                <div className="mt-1">
                                    <input
                                        id="email"
                                        name="email"
                                        type="email"
                                        autoComplete="email"
                                        required
                                        value={formData.email}
                                        onChange={handleInputChange}
                                        disabled={isBlocked}
                                        className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-100 disabled:cursor-not-allowed"
                                    />
                                </div>
                            </div>

                            <div>
                                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                                    Password
                                </label>
                                <div className="mt-1 relative">
                                    <input
                                        id="password"
                                        name="password"
                                        type={showPassword ? "text" : "password"}
                                        autoComplete="current-password"
                                        required
                                        value={formData.password}
                                        onChange={handleInputChange}
                                        disabled={isBlocked}
                                        className="appearance-none block w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-100 disabled:cursor-not-allowed"
                                    />
                                    <button
                                        type="button"
                                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                                        onClick={() => setShowPassword(!showPassword)}
                                        disabled={isBlocked}
                                    >
                                        <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'} text-gray-400 hover:text-gray-600`}></i>
                                    </button>
                                </div>
                            </div>

                            <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                    <input
                                        id="remember-me"
                                        name="rememberMe"
                                        type="checkbox"
                                        checked={formData.rememberMe}
                                        onChange={handleInputChange}
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    />
                                    <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900">
                                        Remember me
                                    </label>
                                </div>

                                <div className="text-sm">
                                    <a href="/forgot-password" className="font-medium text-blue-600 hover:text-blue-500">
                                        Forgot your password?
                                    </a>
                                </div>
                            </div>

                            {error && (
                                <div className="rounded-md bg-red-50 p-4">
                                    <div className="flex">
                                        <div className="flex-shrink-0">
                                            <i className="fas fa-exclamation-circle text-red-400"></i>
                                        </div>
                                        <div className="ml-3">
                                            <h3 className="text-sm font-medium text-red-800">
                                                {error}
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            )}

                            <div>
                                <Button
                                    type="submit"
                                    loading={loading}
                                    disabled={loading || isBlocked}
                                    className="w-full"
                                >
                                    {isBlocked ? `Blocked (${Math.ceil(blockTimeRemaining / 60)}m remaining)` : 'Sign in'}
                                </Button>
                            </div>
                        </form>

                        <div className="mt-6">
                            <div className="relative">
                                <div className="absolute inset-0 flex items-center">
                                    <div className="w-full border-t border-gray-300"></div>
                                </div>
                                <div className="relative flex justify-center text-sm">
                                    <span className="px-2 bg-white text-gray-500">
                                        Or continue with
                                    </span>
                                </div>
                            </div>

                            <div className="mt-6 grid grid-cols-2 gap-3">
                                <Button
                                    type="button"
                                    variant="secondary"
                                    className="w-full"
                                    onClick={() => window.location.href = '/auth/google'}
                                >
                                    <i className="fab fa-google mr-2"></i>
                                    Google
                                </Button>
                                <Button
                                    type="button"
                                    variant="secondary"
                                    className="w-full"
                                    onClick={() => window.location.href = '/auth/microsoft'}
                                >
                                    <i className="fab fa-microsoft mr-2"></i>
                                    Microsoft
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('Login page error:', error);
        // Report error to error handler if available
        if (window.ErrorHandler && window.ErrorHandler.handleError) {
            window.ErrorHandler.handleError(error);
        }
        return null;
    }
}
