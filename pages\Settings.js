function Settings() {
    try {
        const [formData, setFormData] = React.useState({
            companyName: '',
            companyEmail: '',
            companyPhone: '',
            companyAddress: '',
            companyWebsite: '',
            taxRate: 0,
            currency: 'INR',
            dateFormat: 'DD/MM/YYYY',
            theme: 'light',
            logo: '',
            signature: '',
            companyGST: '',
            authorizedName: '',
            bankDetails: '',
            upiId: '',
            defaultPaymentTerms: '',
            defaultNotes: '',
            notifications: {
                email: true,
                browser: true,
                invoice: true,
                quotation: true,
                contract: true
            },
            templates: {
                paperSize: 'a4',
                orientation: 'portrait',
                margins: {
                    top: 15,
                    right: 15,
                    bottom: 15,
                    left: 15
                },
                invoiceTemplate: 'modern',
                quotationTemplate: 'classic',
                contractTemplate: 'professional',
                headerColor: '#3b82f6',
                accentColor: '#1e3a8a',
                fontFamily: 'Segoe UI, sans-serif',
                fontSize: 'medium',
                showLogo: true,
                showSignature: true,
                showWatermark: true
            }
        });

        const [loading, setLoading] = React.useState(true);
        const [saving, setSaving] = React.useState(false);
        const [notification, setNotification] = React.useState(null);
        const [logoPreview, setLogoPreview] = React.useState('');
        const [signaturePreview, setSignaturePreview] = React.useState('');
        const [activeTab, setActiveTab] = React.useState('company');
        const [settingsId, setSettingsId] = React.useState(null);
        const [showPreview, setShowPreview] = React.useState(false);
        const [previewType, setPreviewType] = React.useState('invoice');

        React.useEffect(() => {
            loadSettings();
        }, []);

        const loadSettings = async () => {
            try {
                setLoading(true);
                const token = localStorage.getItem('authToken');
                const response = await fetch(window.getApiUrl('/settings'), {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.items && data.items.length > 0) {
                        const settings = data.items[0];
                        setSettingsId(settings.objectId);
                    
                    // Ensure templates object exists with default values
                    const loadedData = settings.objectData || {};
                    const existingTemplates = loadedData.templates || {};

                    // Merge existing templates with defaults, preserving existing values
                    const templates = {
                        paperSize: existingTemplates.paperSize || 'a4',
                        orientation: existingTemplates.orientation || 'portrait',
                        margins: existingTemplates.margins || { top: 15, right: 15, bottom: 15, left: 15 },
                        invoiceTemplate: existingTemplates.invoiceTemplate || 'modern',
                        quotationTemplate: existingTemplates.quotationTemplate || 'classic',
                        contractTemplate: existingTemplates.contractTemplate || 'professional',
                        headerColor: existingTemplates.headerColor || '#3b82f6',
                        accentColor: existingTemplates.accentColor || '#1e3a8a',
                        fontFamily: existingTemplates.fontFamily || 'Segoe UI, sans-serif',
                        fontSize: existingTemplates.fontSize || 'medium',
                        showLogo: existingTemplates.showLogo !== undefined ? existingTemplates.showLogo : true,
                        showSignature: existingTemplates.showSignature !== undefined ? existingTemplates.showSignature : true,
                        showWatermark: existingTemplates.showWatermark !== undefined ? existingTemplates.showWatermark : true,
                        // Enhanced settings
                        lineHeight: existingTemplates.lineHeight || 'normal',
                        headerStyle: existingTemplates.headerStyle || 'modern',
                        footerText: existingTemplates.footerText || '',
                        showPageNumbers: existingTemplates.showPageNumbers !== undefined ? existingTemplates.showPageNumbers : true,
                        showCompanyDetails: existingTemplates.showCompanyDetails !== undefined ? existingTemplates.showCompanyDetails : true,
                        showBankDetails: existingTemplates.showBankDetails !== undefined ? existingTemplates.showBankDetails : true,
                        logoPosition: existingTemplates.logoPosition || 'top-right',
                        logoSize: existingTemplates.logoSize || 'medium',
                        borderStyle: existingTemplates.borderStyle || 'minimal',
                        tableStyle: existingTemplates.tableStyle || 'striped'
                    };
                    
                    // Ensure notifications object exists
                    const notifications = loadedData.notifications || {
                        email: true,
                        browser: true,
                        invoice: true,
                        quotation: true,
                        contract: true
                    };
                    
                    // Set form data with proper structure
                    setFormData({
                        ...loadedData,
                        templates,
                        notifications
                    });
                    
                    if (loadedData.logo) setLogoPreview(loadedData.logo);
                    if (loadedData.signature) setSignaturePreview(loadedData.signature);
                    }
                }
            } catch (error) {
                console.error('Error loading settings:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to load settings'
                });
            } finally {
                setLoading(false);
            }
        };

        const handleInputChange = (e) => {
            const { name, value, type, checked } = e.target;
            setFormData(prev => ({
                ...prev,
                [name]: type === 'checkbox' ? checked : value
            }));
        };

        const handleTemplateChange = (e) => {
            const { name, value, type, checked } = e.target;
            setFormData(prev => ({
                ...prev,
                templates: {
                    ...prev.templates,
                    [name]: type === 'checkbox' ? checked : value
                }
            }));
        };

        const handleMarginChange = (e) => {
            const { name, value } = e.target;
            console.log('handleMarginChange called:', { name, value });

            setFormData(prev => {
                const newFormData = {
                    ...prev,
                    templates: {
                        ...prev.templates,
                        margins: {
                            ...(prev.templates && prev.templates.margins ? prev.templates.margins : { top: 15, right: 15, bottom: 15, left: 15 }),
                            [name]: parseInt(value) || 0
                        }
                    }
                };
                console.log('Updated formData margins:', newFormData.templates.margins);
                return newFormData;
            });
        };

        const handleNotificationChange = (key) => {
            setFormData(prev => ({
                ...prev,
                notifications: {
                    ...(prev.notifications || { email: true, browser: true, invoice: true, quotation: true, contract: true }),
                    [key]: !(prev.notifications && prev.notifications[key])
                }
            }));
        };

        const handleFileUpload = (e, type) => {
            const file = e.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onloadend = () => {
                if (type === 'logo') {
                    setLogoPreview(reader.result);
                    setFormData(prev => ({
                        ...prev,
                        logo: reader.result
                    }));
                } else if (type === 'signature') {
                    setSignaturePreview(reader.result);
                    setFormData(prev => ({
                        ...prev,
                        signature: reader.result
                    }));
                }
            };
            reader.readAsDataURL(file);
        };

        const handleSubmit = async (e) => {
            e.preventDefault();
            try {
                setSaving(true);
                const token = localStorage.getItem('authToken');
                let response;

                if (settingsId) {
                    // Update existing settings
                    response = await fetch(window.getApiUrl(`/settings/${settingsId}`), {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    });
                } else {
                    // Create new settings
                    response = await fetch(window.getApiUrl('/settings'), {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    });
                }

                if (response.ok) {
                    if (!settingsId) {
                        const data = await response.json();
                        setSettingsId(data.objectId);
                    }
                } else {
                    throw new Error('Failed to save settings');
                }
                setNotification({
                    type: 'success',
                    message: 'Settings saved successfully'
                });
            } catch (error) {
                console.error('Error saving settings:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to save settings'
                });
            } finally {
                setSaving(false);
            }
        };

        const handleOpenPreview = (type) => {
            setPreviewType(type);
            setShowPreview(true);
        };

        const handleClosePreview = () => {
            setShowPreview(false);
        };

        if (loading) {
            return (
                <div className="flex justify-center items-center h-64">
                    <i className="fas fa-spinner fa-spin fa-2x text-blue-500"></i>
                </div>
            );
        }

        return (
            <div data-name="settings-page">
                <h1 className="text-2xl font-bold mb-6">Settings</h1>

                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}

                <div className="bg-white rounded-lg shadow">
                    <div className="border-b border-gray-200">
                        <nav className="flex -mb-px">
                            <button
                                className={`px-6 py-4 text-sm font-medium ${
                                    activeTab === 'company'
                                        ? 'border-b-2 border-blue-500 text-blue-600'
                                        : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                                onClick={() => setActiveTab('company')}
                            >
                                Company Information
                            </button>
                            <button
                                className={`px-6 py-4 text-sm font-medium ${
                                    activeTab === 'branding'
                                        ? 'border-b-2 border-blue-500 text-blue-600'
                                        : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                                onClick={() => setActiveTab('branding')}
                            >
                                Branding
                            </button>
                            <button
                                className={`px-6 py-4 text-sm font-medium ${
                                    activeTab === 'templates'
                                        ? 'border-b-2 border-blue-500 text-blue-600'
                                        : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                                onClick={() => setActiveTab('templates')}
                            >
                                Templates
                            </button>
                            <button
                                className={`px-6 py-4 text-sm font-medium ${
                                    activeTab === 'preferences'
                                        ? 'border-b-2 border-blue-500 text-blue-600'
                                        : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                                onClick={() => setActiveTab('preferences')}
                            >
                                Preferences
                            </button>
                            <button
                                className={`px-6 py-4 text-sm font-medium ${
                                    activeTab === 'notifications'
                                        ? 'border-b-2 border-blue-500 text-blue-600'
                                        : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                                onClick={() => setActiveTab('notifications')}
                            >
                                Notifications
                            </button>
                        </nav>
                    </div>

                    <div className="p-6">
                        <form onSubmit={handleSubmit}>
                            {activeTab === 'company' && (
                                <CompanySettings 
                                    formData={formData} 
                                    handleInputChange={handleInputChange} 
                                />
                            )}

                            {activeTab === 'branding' && (
                                <BrandingSettings 
                                    formData={formData}
                                    logoPreview={logoPreview}
                                    signaturePreview={signaturePreview}
                                    handleFileUpload={handleFileUpload}
                                    setLogoPreview={setLogoPreview}
                                    setSignaturePreview={setSignaturePreview}
                                    setFormData={setFormData}
                                />
                            )}

                            {activeTab === 'templates' && (
                                <TemplateSettings 
                                    formData={formData}
                                    handleTemplateChange={handleTemplateChange}
                                    handleMarginChange={handleMarginChange}
                                    handleOpenPreview={handleOpenPreview}
                                />
                            )}

                            {activeTab === 'preferences' && (
                                <PreferenceSettings 
                                    formData={formData}
                                    handleInputChange={handleInputChange}
                                />
                            )}

                            {activeTab === 'notifications' && (
                                <NotificationSettings 
                                    formData={formData}
                                    handleNotificationChange={handleNotificationChange}
                                />
                            )}

                            <div className="mt-6 flex justify-end">
                                <Button
                                    type="submit"
                                    loading={saving}
                                    disabled={saving}
                                >
                                    Save Settings
                                </Button>
                            </div>
                        </form>
                    </div>
                </div>

                {showPreview && (
                    <Modal
                        isOpen={showPreview}
                        onClose={handleClosePreview}
                        title={`${previewType.charAt(0).toUpperCase() + previewType.slice(1)} Preview`}
                        size="xl"
                    >
                        <TemplatePreview 
                            type={previewType} 
                            companyInfo={formData} 
                            onClose={handleClosePreview}
                        />
                    </Modal>
                )}
            </div>
        );
    } catch (error) {
        console.error('Settings page error:', error);
        // Report error to error handler if available
        if (window.ErrorHandler && window.ErrorHandler.handleError) {
            window.ErrorHandler.handleError(error);
        }
        return null;
    }
}
