<?php
/**
 * Settings Handler
 * Manages company settings operations
 */

require_once __DIR__ . '/email-handler.php';

// Function to get settings for the current company
function getCompanySettings() {
    global $conn;
    
    // Get current user and enforce authentication
    $currentUser = getCurrentUser();
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        return;
    }
    
    $companyId = $currentUser['company_id'];
    
    // Query to get settings for the company
    $sql = "SELECT * FROM settings WHERE company_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $companyId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $items = [];
    while ($row = $result->fetch_assoc()) {
        $items[] = [
            'objectId' => $row['object_id'],
            'objectType' => 'settings',
            'objectData' => [
                'companyName' => $row['company_name'],
                'companyEmail' => $row['company_email'],
                'companyPhone' => $row['company_phone'],
                'companyAddress' => $row['company_address'],
                'companyWebsite' => $row['company_website'],
                'taxRate' => $row['tax_rate'],
                'currency' => $row['currency'],
                'dateFormat' => $row['date_format'],
                'theme' => $row['theme'],
                'logo' => $row['logo'],
                'signature' => $row['signature'],
                'companyGST' => $row['company_gst'],
                'authorizedName' => $row['authorized_name'],
                'bankDetails' => $row['bank_details'],
                'upiId' => $row['upi_id'],
                'defaultPaymentTerms' => $row['default_payment_terms'],
                'defaultNotes' => $row['default_notes'],
                'notifications' => json_decode($row['notifications'], true),
                'templates' => json_decode($row['templates'], true),
                'emailSettings' => loadEmailSettingsForCompany($companyId)
            ]
        ];
    }
    
    // Return response
    echo json_encode([
        'success' => true,
        'items' => $items,
        'total' => count($items)
    ]);
}

// Function to create settings
function createSettings($data) {
    global $conn;
    
    // Get current user and enforce authentication
    $currentUser = getCurrentUser();
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        return;
    }
    
    $companyId = $currentUser['company_id'];
    $objectId = generateId();
    
    // Check if settings already exist for this company
    $sql = "SELECT COUNT(*) as count FROM settings WHERE company_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $companyId);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    if ($row['count'] > 0) {
        http_response_code(409);
        echo json_encode(['error' => 'Settings already exist for this company']);
        return;
    }
    
    // Extract data from request
    $companyName = isset($data['companyName']) ? sanitizeInput($data['companyName']) : '';
    $companyEmail = isset($data['companyEmail']) ? sanitizeInput($data['companyEmail']) : '';
    $companyPhone = isset($data['companyPhone']) ? sanitizeInput($data['companyPhone']) : '';
    $companyAddress = isset($data['companyAddress']) ? sanitizeInput($data['companyAddress']) : '';
    $companyWebsite = isset($data['companyWebsite']) ? sanitizeInput($data['companyWebsite']) : '';
    $taxRate = isset($data['taxRate']) ? floatval($data['taxRate']) : 0;
    $currency = isset($data['currency']) ? sanitizeInput($data['currency']) : 'INR';
    $dateFormat = isset($data['dateFormat']) ? sanitizeInput($data['dateFormat']) : 'DD/MM/YYYY';
    $theme = isset($data['theme']) ? sanitizeInput($data['theme']) : 'light';
    $logo = isset($data['logo']) ? sanitizeInput($data['logo']) : '';
    $signature = isset($data['signature']) ? sanitizeInput($data['signature']) : '';
    $companyGST = isset($data['companyGST']) ? sanitizeInput($data['companyGST']) : '';
    $authorizedName = isset($data['authorizedName']) ? sanitizeInput($data['authorizedName']) : '';
    $bankDetails = isset($data['bankDetails']) ? sanitizeInput($data['bankDetails']) : '';
    $upiId = isset($data['upiId']) ? sanitizeInput($data['upiId']) : '';
    $defaultPaymentTerms = isset($data['defaultPaymentTerms']) ? sanitizeInput($data['defaultPaymentTerms']) : '';
    $defaultNotes = isset($data['defaultNotes']) ? sanitizeInput($data['defaultNotes']) : '';
    
    // Handle JSON fields
    $notifications = isset($data['notifications']) ? json_encode($data['notifications']) : '{}';
    $templates = isset($data['templates']) ? json_encode($data['templates']) : '{}';
    
    // Insert settings
    $sql = "INSERT INTO settings (
        object_id, company_id, company_name, company_email, company_phone, 
        company_address, company_website, tax_rate, currency, date_format, 
        theme, logo, signature, company_gst, authorized_name, bank_details, 
        upi_id, default_payment_terms, default_notes, notifications, templates
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param(
        "sssssssdsssssssssssss",
        $objectId, $companyId, $companyName, $companyEmail, $companyPhone,
        $companyAddress, $companyWebsite, $taxRate, $currency, $dateFormat,
        $theme, $logo, $signature, $companyGST, $authorizedName, $bankDetails,
        $upiId, $defaultPaymentTerms, $defaultNotes, $notifications, $templates
    );
    
    if ($stmt->execute()) {
        // Return success response
        echo json_encode([
            'success' => true,
            'objectId' => $objectId,
            'message' => 'Settings created successfully'
        ]);
    } else {
        // Return error response
        http_response_code(500);
        echo json_encode([
            'error' => 'Failed to create settings',
            'details' => $conn->error
        ]);
    }
}

// Function to update settings
function updateCompanySettings($objectId, $data) {
    global $conn;
    
    // Get current user and enforce authentication
    $currentUser = getCurrentUser();
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        return;
    }
    
    $companyId = $currentUser['company_id'];
    $objectId = sanitizeInput($objectId);
    
    // Check if settings exist and belong to the company
    $sql = "SELECT COUNT(*) as count FROM settings WHERE object_id = ? AND company_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ss", $objectId, $companyId);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    if ($row['count'] == 0) {
        http_response_code(404);
        echo json_encode(['error' => 'Settings not found or access denied']);
        return;
    }
    
    // Extract data from request
    $companyName = isset($data['companyName']) ? sanitizeInput($data['companyName']) : '';
    $companyEmail = isset($data['companyEmail']) ? sanitizeInput($data['companyEmail']) : '';
    $companyPhone = isset($data['companyPhone']) ? sanitizeInput($data['companyPhone']) : '';
    $companyAddress = isset($data['companyAddress']) ? sanitizeInput($data['companyAddress']) : '';
    $companyWebsite = isset($data['companyWebsite']) ? sanitizeInput($data['companyWebsite']) : '';
    $taxRate = isset($data['taxRate']) ? floatval($data['taxRate']) : 0;
    $currency = isset($data['currency']) ? sanitizeInput($data['currency']) : 'INR';
    $dateFormat = isset($data['dateFormat']) ? sanitizeInput($data['dateFormat']) : 'DD/MM/YYYY';
    $theme = isset($data['theme']) ? sanitizeInput($data['theme']) : 'light';
    $logo = isset($data['logo']) ? sanitizeInput($data['logo']) : '';
    $signature = isset($data['signature']) ? sanitizeInput($data['signature']) : '';
    $companyGST = isset($data['companyGST']) ? sanitizeInput($data['companyGST']) : '';
    $authorizedName = isset($data['authorizedName']) ? sanitizeInput($data['authorizedName']) : '';
    $bankDetails = isset($data['bankDetails']) ? sanitizeInput($data['bankDetails']) : '';
    $upiId = isset($data['upiId']) ? sanitizeInput($data['upiId']) : '';
    $defaultPaymentTerms = isset($data['defaultPaymentTerms']) ? sanitizeInput($data['defaultPaymentTerms']) : '';
    $defaultNotes = isset($data['defaultNotes']) ? sanitizeInput($data['defaultNotes']) : '';
    
    // Handle JSON fields
    $notifications = isset($data['notifications']) ? json_encode($data['notifications']) : '{}';
    $templates = isset($data['templates']) ? json_encode($data['templates']) : '{}';
    
    // Update settings
    $sql = "UPDATE settings SET 
        company_name = ?, company_email = ?, company_phone = ?, 
        company_address = ?, company_website = ?, tax_rate = ?, 
        currency = ?, date_format = ?, theme = ?, logo = ?, 
        signature = ?, company_gst = ?, authorized_name = ?, 
        bank_details = ?, upi_id = ?, default_payment_terms = ?, 
        default_notes = ?, notifications = ?, templates = ?
        WHERE object_id = ? AND company_id = ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param(
        "sssssdsssssssssssssss",
        $companyName, $companyEmail, $companyPhone,
        $companyAddress, $companyWebsite, $taxRate,
        $currency, $dateFormat, $theme, $logo,
        $signature, $companyGST, $authorizedName,
        $bankDetails, $upiId, $defaultPaymentTerms,
        $defaultNotes, $notifications, $templates,
        $objectId, $companyId
    );
    
    if ($stmt->execute()) {
        // Save email settings if provided
        if (isset($data['emailSettings'])) {
            saveEmailSettings($data['emailSettings'], $companyId);
        }

        // Return success response
        echo json_encode([
            'success' => true,
            'message' => 'Settings updated successfully'
        ]);
    } else {
        // Return error response
        http_response_code(500);
        echo json_encode([
            'error' => 'Failed to update settings',
            'details' => $conn->error
        ]);
    }
}

// Function to handle settings requests
function handleSettings($action = null, $objectId = null) {
    global $conn;
    
    // Get request method
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            getCompanySettings();
            break;
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            createSettings($input);
            break;
        case 'PUT':
            if (!$objectId) {
                http_response_code(400);
                echo json_encode(['error' => 'Object ID is required for update']);
                return;
            }
            $input = json_decode(file_get_contents('php://input'), true);
            updateCompanySettings($objectId, $input);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
}

// Helper function to load email settings for a company
function loadEmailSettingsForCompany($companyId) {
    $emailSettings = loadEmailSettings($companyId);

    // Return default settings if none found
    if (!$emailSettings) {
        return [
            'smtp_host' => '',
            'smtp_port' => '587',
            'smtp_username' => '',
            'smtp_password' => '',
            'smtp_encryption' => 'tls',
            'from_email' => '',
            'from_name' => '',
            'reply_to' => '',
            'enabled' => false
        ];
    }

    return $emailSettings;
}

?>