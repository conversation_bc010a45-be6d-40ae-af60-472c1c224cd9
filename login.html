<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign In - Bizma</title>
    <meta name="description" content="Sign in to your Bizma account to manage your business operations">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    
    <!-- React -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    
    <!-- Babel for JSX -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
</head>
<body>
    <div id="root"></div>

    <!-- Load Layout Components -->
    <script type="text/babel" src="components/layout/WebsiteHeader.js"></script>
    <script type="text/babel" src="components/layout/WebsiteFooter.js"></script>
    
    <!-- Load Common Components -->
    <script type="text/babel" src="components/common/Button.js"></script>
    <script type="text/babel" src="components/common/Modal.js"></script>
    
    <!-- Load Auth Components -->
    <script type="text/babel" src="components/auth/LoginForm.js"></script>
    
    <script type="text/babel">
        // Login Page Component with Header and Footer
        function LoginPage() {
            const [formData, setFormData] = React.useState({
                email: '',
                password: '',
                rememberMe: false
            });
            const [loading, setLoading] = React.useState(false);
            const [error, setError] = React.useState('');
            const [showPassword, setShowPassword] = React.useState(false);

            React.useEffect(() => {
                document.title = 'Sign In - Bizma';
            }, []);

            const handleSubmit = async (e) => {
                e.preventDefault();
                setLoading(true);
                setError('');

                try {
                    const response = await fetch('api/auth-handler.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'login',
                            email: formData.email,
                            password: formData.password,
                            remember_me: formData.rememberMe
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        // Store auth token if provided
                        if (data.token) {
                            localStorage.setItem('auth_token', data.token);
                        }
                        
                        // Redirect to dashboard
                        window.location.href = 'dashboard.html';
                    } else {
                        setError(data.message || 'Login failed. Please try again.');
                    }
                } catch (error) {
                    console.error('Login error:', error);
                    setError('Network error. Please check your connection and try again.');
                } finally {
                    setLoading(false);
                }
            };

            const handleInputChange = (e) => {
                const { name, value, type, checked } = e.target;
                setFormData(prev => ({
                    ...prev,
                    [name]: type === 'checkbox' ? checked : value
                }));
                setError('');
            };

            return (
                <div className="min-h-screen bg-white">
                    {/* Header */}
                    <WebsiteHeader currentPage="login" />

                    {/* Login Form Section */}
                    <div className="bg-gray-50 py-12">
                        <div className="max-w-md mx-auto px-4 sm:px-6 lg:px-8">
                            <div className="bg-white rounded-lg shadow-lg p-8">
                                <div className="text-center mb-8">
                                    <div className="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                                        <span className="text-white font-bold text-2xl">B</span>
                                    </div>
                                    <h1 className="text-3xl font-bold text-gray-900 mb-2">
                                        Sign in to your account
                                    </h1>
                                    <p className="text-gray-600">
                                        Or{' '}
                                        <a href="register.html" className="text-blue-600 hover:text-blue-500 font-medium">
                                            start your 14-day free trial
                                        </a>
                                    </p>
                                </div>

                                <form onSubmit={handleSubmit} className="space-y-6">
                                    <div>
                                        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                                            Email address
                                        </label>
                                        <input
                                            id="email"
                                            name="email"
                                            type="email"
                                            autoComplete="email"
                                            required
                                            value={formData.email}
                                            onChange={handleInputChange}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="Enter your email"
                                        />
                                    </div>

                                    <div>
                                        <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                                            Password
                                        </label>
                                        <div className="relative">
                                            <input
                                                id="password"
                                                name="password"
                                                type={showPassword ? "text" : "password"}
                                                autoComplete="current-password"
                                                required
                                                value={formData.password}
                                                onChange={handleInputChange}
                                                className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                placeholder="Enter your password"
                                            />
                                            <button
                                                type="button"
                                                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                                                onClick={() => setShowPassword(!showPassword)}
                                            >
                                                <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'} text-gray-400 hover:text-gray-600`}></i>
                                            </button>
                                        </div>
                                    </div>

                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center">
                                            <input
                                                id="remember-me"
                                                name="rememberMe"
                                                type="checkbox"
                                                checked={formData.rememberMe}
                                                onChange={handleInputChange}
                                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                            />
                                            <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900">
                                                Remember me
                                            </label>
                                        </div>

                                        <div className="text-sm">
                                            <a href="forgot-password.html" className="font-medium text-blue-600 hover:text-blue-500">
                                                Forgot your password?
                                            </a>
                                        </div>
                                    </div>

                                    {error && (
                                        <div className="rounded-lg bg-red-50 p-4 border border-red-200">
                                            <div className="flex">
                                                <div className="flex-shrink-0">
                                                    <i className="fas fa-exclamation-circle text-red-400"></i>
                                                </div>
                                                <div className="ml-3">
                                                    <p className="text-sm font-medium text-red-800">
                                                        {error}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    <button
                                        type="submit"
                                        disabled={loading}
                                        className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                    >
                                        {loading ? (
                                            <>
                                                <i className="fas fa-spinner fa-spin mr-2"></i>
                                                Signing in...
                                            </>
                                        ) : (
                                            'Sign in'
                                        )}
                                    </button>
                                </form>

                                <div className="mt-6">
                                    <div className="relative">
                                        <div className="absolute inset-0 flex items-center">
                                            <div className="w-full border-t border-gray-300"></div>
                                        </div>
                                        <div className="relative flex justify-center text-sm">
                                            <span className="px-2 bg-white text-gray-500">
                                                New to Bizma?
                                            </span>
                                        </div>
                                    </div>

                                    <div className="mt-6">
                                        <a
                                            href="register.html"
                                            className="w-full flex justify-center py-3 px-4 border border-blue-600 rounded-lg shadow-sm text-sm font-medium text-blue-600 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                                        >
                                            Create your account
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Footer */}
                    <WebsiteFooter currentPage="login" />
                </div>
            );
        }

        const { createRoot } = ReactDOM;
        const root = createRoot(document.getElementById('root'));
        root.render(<LoginPage />);
    </script>
</body>
</html>
