<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Account - Bizma</title>
    <meta name="description" content="Create your Bizma account and start your 14-day free trial">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    
    <!-- React -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    
    <!-- Babel for JSX -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
</head>
<body>
    <div id="root"></div>

    <!-- Load Layout Components -->
    <script type="text/babel" src="components/layout/WebsiteHeader.js"></script>
    <script type="text/babel" src="components/layout/WebsiteFooter.js"></script>
    
    <!-- Load Pricing Components -->
    <script type="text/babel" src="components/pricing/SimplePricingPlans.js"></script>
    
    <script type="text/babel">
        // Registration Page Component with Header and Footer
        function RegisterPage() {
            const [currentStep, setCurrentStep] = React.useState(1);
            const [formData, setFormData] = React.useState({
                // Personal Info
                name: '',
                email: '',
                password: '',
                confirmPassword: '',
                
                // Company Info
                company_name: '',
                company_size: '1-10',
                industry: 'Technology',
                
                // Plan Selection
                selected_plan: 'basic',
                billing_cycle: 'monthly',
                plan_price: 500
            });
            const [loading, setLoading] = React.useState(false);
            const [error, setError] = React.useState('');
            const [showPassword, setShowPassword] = React.useState(false);

            React.useEffect(() => {
                document.title = 'Create Account - Bizma';
            }, []);

            const handlePlanSelect = (planData) => {
                setFormData(prev => ({
                    ...prev,
                    selected_plan: planData.planId,
                    billing_cycle: planData.billingCycle,
                    plan_price: planData.price
                }));
                setCurrentStep(2);
            };

            const handleSubmit = async (e) => {
                e.preventDefault();
                
                if (formData.password !== formData.confirmPassword) {
                    setError('Passwords do not match');
                    return;
                }

                setLoading(true);
                setError('');

                try {
                    const response = await fetch('api/register.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(formData)
                    });

                    const data = await response.json();

                    if (data.success) {
                        // Show success message and redirect
                        alert('Registration successful! Please check your email to verify your account.');
                        window.location.href = 'login.html';
                    } else {
                        setError(data.message || 'Registration failed. Please try again.');
                    }
                } catch (error) {
                    console.error('Registration error:', error);
                    setError('Network error. Please check your connection and try again.');
                } finally {
                    setLoading(false);
                }
            };

            const handleInputChange = (e) => {
                const { name, value } = e.target;
                setFormData(prev => ({
                    ...prev,
                    [name]: value
                }));
                setError('');
            };

            const nextStep = () => {
                if (currentStep < 3) {
                    setCurrentStep(currentStep + 1);
                }
            };

            const prevStep = () => {
                if (currentStep > 1) {
                    setCurrentStep(currentStep - 1);
                }
            };

            return (
                <div className="min-h-screen bg-white">
                    {/* Header */}
                    <WebsiteHeader currentPage="register" />

                    {/* Registration Form Section */}
                    <div className="bg-gray-50 py-12">
                        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
                            
                            {/* Step 1: Plan Selection */}
                            {currentStep === 1 && (
                                <div className="bg-white rounded-lg shadow-lg p-8">
                                    <div className="text-center mb-8">
                                        <div className="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                                            <span className="text-white font-bold text-2xl">B</span>
                                        </div>
                                        <h1 className="text-3xl font-bold text-gray-900 mb-2">
                                            Choose Your Plan
                                        </h1>
                                        <p className="text-gray-600">
                                            Start with our 14-day free trial. No credit card required.
                                        </p>
                                    </div>

                                    <SimplePricingPlans 
                                        onPlanSelect={handlePlanSelect}
                                        showTitle={false}
                                    />

                                    <div className="text-center mt-8">
                                        <p className="text-sm text-gray-500">
                                            Already have an account?{' '}
                                            <a href="login.html" className="text-blue-600 hover:text-blue-500 font-medium">
                                                Sign in here
                                            </a>
                                        </p>
                                    </div>
                                </div>
                            )}

                            {/* Step 2: Personal Information */}
                            {currentStep === 2 && (
                                <div className="bg-white rounded-lg shadow-lg p-8">
                                    <div className="text-center mb-8">
                                        <h1 className="text-3xl font-bold text-gray-900 mb-2">
                                            Personal Information
                                        </h1>
                                        <p className="text-gray-600">
                                            Tell us about yourself
                                        </p>
                                    </div>

                                    <form onSubmit={(e) => { e.preventDefault(); nextStep(); }} className="space-y-6">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div>
                                                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                                                    Full Name *
                                                </label>
                                                <input
                                                    id="name"
                                                    name="name"
                                                    type="text"
                                                    required
                                                    value={formData.name}
                                                    onChange={handleInputChange}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                    placeholder="Enter your full name"
                                                />
                                            </div>

                                            <div>
                                                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                                                    Email Address *
                                                </label>
                                                <input
                                                    id="email"
                                                    name="email"
                                                    type="email"
                                                    required
                                                    value={formData.email}
                                                    onChange={handleInputChange}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                    placeholder="Enter your email"
                                                />
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div>
                                                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                                                    Password *
                                                </label>
                                                <div className="relative">
                                                    <input
                                                        id="password"
                                                        name="password"
                                                        type={showPassword ? "text" : "password"}
                                                        required
                                                        value={formData.password}
                                                        onChange={handleInputChange}
                                                        className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                        placeholder="Create a password"
                                                    />
                                                    <button
                                                        type="button"
                                                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                                                        onClick={() => setShowPassword(!showPassword)}
                                                    >
                                                        <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'} text-gray-400 hover:text-gray-600`}></i>
                                                    </button>
                                                </div>
                                            </div>

                                            <div>
                                                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
                                                    Confirm Password *
                                                </label>
                                                <input
                                                    id="confirmPassword"
                                                    name="confirmPassword"
                                                    type="password"
                                                    required
                                                    value={formData.confirmPassword}
                                                    onChange={handleInputChange}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                    placeholder="Confirm your password"
                                                />
                                            </div>
                                        </div>

                                        <div className="flex justify-between">
                                            <button
                                                type="button"
                                                onClick={prevStep}
                                                className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                                            >
                                                Back
                                            </button>
                                            <button
                                                type="submit"
                                                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                                            >
                                                Continue
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            )}

                            {/* Step 3: Company Information */}
                            {currentStep === 3 && (
                                <div className="bg-white rounded-lg shadow-lg p-8">
                                    <div className="text-center mb-8">
                                        <h1 className="text-3xl font-bold text-gray-900 mb-2">
                                            Company Information
                                        </h1>
                                        <p className="text-gray-600">
                                            Tell us about your business
                                        </p>
                                    </div>

                                    <form onSubmit={handleSubmit} className="space-y-6">
                                        <div>
                                            <label htmlFor="company_name" className="block text-sm font-medium text-gray-700 mb-2">
                                                Company Name *
                                            </label>
                                            <input
                                                id="company_name"
                                                name="company_name"
                                                type="text"
                                                required
                                                value={formData.company_name}
                                                onChange={handleInputChange}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                placeholder="Enter your company name"
                                            />
                                        </div>

                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div>
                                                <label htmlFor="company_size" className="block text-sm font-medium text-gray-700 mb-2">
                                                    Company Size
                                                </label>
                                                <select
                                                    id="company_size"
                                                    name="company_size"
                                                    value={formData.company_size}
                                                    onChange={handleInputChange}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                >
                                                    <option value="1-10">1-10 employees</option>
                                                    <option value="11-50">11-50 employees</option>
                                                    <option value="51-200">51-200 employees</option>
                                                    <option value="201-500">201-500 employees</option>
                                                    <option value="500+">500+ employees</option>
                                                </select>
                                            </div>

                                            <div>
                                                <label htmlFor="industry" className="block text-sm font-medium text-gray-700 mb-2">
                                                    Industry
                                                </label>
                                                <select
                                                    id="industry"
                                                    name="industry"
                                                    value={formData.industry}
                                                    onChange={handleInputChange}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                >
                                                    <option value="Technology">Technology</option>
                                                    <option value="Healthcare">Healthcare</option>
                                                    <option value="Finance">Finance</option>
                                                    <option value="Education">Education</option>
                                                    <option value="Retail">Retail</option>
                                                    <option value="Manufacturing">Manufacturing</option>
                                                    <option value="Services">Services</option>
                                                    <option value="Other">Other</option>
                                                </select>
                                            </div>
                                        </div>

                                        {error && (
                                            <div className="rounded-lg bg-red-50 p-4 border border-red-200">
                                                <div className="flex">
                                                    <div className="flex-shrink-0">
                                                        <i className="fas fa-exclamation-circle text-red-400"></i>
                                                    </div>
                                                    <div className="ml-3">
                                                        <p className="text-sm font-medium text-red-800">
                                                            {error}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        <div className="flex justify-between">
                                            <button
                                                type="button"
                                                onClick={prevStep}
                                                className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                                            >
                                                Back
                                            </button>
                                            <button
                                                type="submit"
                                                disabled={loading}
                                                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                            >
                                                {loading ? (
                                                    <>
                                                        <i className="fas fa-spinner fa-spin mr-2"></i>
                                                        Creating Account...
                                                    </>
                                                ) : (
                                                    'Create Account'
                                                )}
                                            </button>
                                        </div>
                                    </form>

                                    <div className="text-center mt-6">
                                        <p className="text-sm text-gray-500">
                                            By creating an account, you agree to our{' '}
                                            <a href="terms.html" className="text-blue-600 hover:text-blue-500">Terms & Conditions</a>
                                            {' '}and{' '}
                                            <a href="privacy.html" className="text-blue-600 hover:text-blue-500">Privacy Policy</a>
                                        </p>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Footer */}
                    <WebsiteFooter currentPage="register" />
                </div>
            );
        }

        const { createRoot } = ReactDOM;
        const root = createRoot(document.getElementById('root'));
        root.render(<RegisterPage />);
    </script>
</body>
</html>
