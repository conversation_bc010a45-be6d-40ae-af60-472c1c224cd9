// Website Header Component
function WebsiteHeader({ currentPage = 'home' }) {
    const [isMenuOpen, setIsMenuOpen] = React.useState(false);

    const navigation = [
        { name: 'Home', href: 'index.html', id: 'home' },
        { name: 'About', href: 'about.html', id: 'about' },
        { name: 'Contact', href: 'contact.html', id: 'contact' },
        { name: 'Privacy', href: 'privacy.html', id: 'privacy' },
        { name: 'Terms', href: 'terms.html', id: 'terms' },
        { name: 'Refund', href: 'refund.html', id: 'refund' }
    ];

    const scrollToSection = (sectionId) => {
        if (currentPage === 'home') {
            const element = document.getElementById(sectionId);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
            }
        } else {
            // If not on home page, navigate to home with hash
            window.location.href = `index.html#${sectionId}`;
        }
        setIsMenuOpen(false);
    };

    return (
        <header className="bg-white shadow-sm sticky top-0 z-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center py-4">
                    {/* Logo */}
                    <div className="flex items-center">
                        <a href="index.html" className="flex items-center space-x-2">
                            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                                <span className="text-white font-bold text-lg">B</span>
                            </div>
                            <span className="text-2xl font-bold text-gray-900">Bizma</span>
                        </a>
                    </div>

                    {/* Desktop Navigation */}
                    <nav className="hidden md:flex items-center space-x-8">
                        <a 
                            href="index.html"
                            className={`text-gray-700 hover:text-blue-600 transition-colors ${
                                currentPage === 'home' ? 'text-blue-600 font-medium' : ''
                            }`}
                        >
                            Home
                        </a>
                        {currentPage === 'home' && (
                            <>
                                <button 
                                    onClick={() => scrollToSection('features')}
                                    className="text-gray-700 hover:text-blue-600 transition-colors"
                                >
                                    Features
                                </button>
                                <button 
                                    onClick={() => scrollToSection('pricing')}
                                    className="text-gray-700 hover:text-blue-600 transition-colors"
                                >
                                    Pricing
                                </button>
                            </>
                        )}
                        <a 
                            href="about.html"
                            className={`text-gray-700 hover:text-blue-600 transition-colors ${
                                currentPage === 'about' ? 'text-blue-600 font-medium' : ''
                            }`}
                        >
                            About
                        </a>
                        <a 
                            href="contact.html"
                            className={`text-gray-700 hover:text-blue-600 transition-colors ${
                                currentPage === 'contact' ? 'text-blue-600 font-medium' : ''
                            }`}
                        >
                            Contact
                        </a>
                        
                        {/* CTA Buttons */}
                        <div className="flex items-center space-x-4">
                            <a 
                                href="login.html" 
                                className="text-gray-700 hover:text-blue-600 transition-colors"
                            >
                                Sign In
                            </a>
                            <a 
                                href="register.html" 
                                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                            >
                                Get Started
                            </a>
                        </div>
                    </nav>

                    {/* Mobile menu button */}
                    <div className="md:hidden">
                        <button
                            onClick={() => setIsMenuOpen(!isMenuOpen)}
                            className="text-gray-700 hover:text-blue-600 focus:outline-none"
                        >
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                {isMenuOpen ? (
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                ) : (
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                )}
                            </svg>
                        </button>
                    </div>
                </div>

                {/* Mobile Navigation */}
                {isMenuOpen && (
                    <div className="md:hidden border-t border-gray-200 py-4">
                        <div className="flex flex-col space-y-4">
                            <a 
                                href="index.html"
                                className={`text-gray-700 hover:text-blue-600 transition-colors ${
                                    currentPage === 'home' ? 'text-blue-600 font-medium' : ''
                                }`}
                                onClick={() => setIsMenuOpen(false)}
                            >
                                Home
                            </a>
                            {currentPage === 'home' && (
                                <>
                                    <button 
                                        onClick={() => scrollToSection('features')}
                                        className="text-left text-gray-700 hover:text-blue-600 transition-colors"
                                    >
                                        Features
                                    </button>
                                    <button 
                                        onClick={() => scrollToSection('pricing')}
                                        className="text-left text-gray-700 hover:text-blue-600 transition-colors"
                                    >
                                        Pricing
                                    </button>
                                </>
                            )}
                            <a 
                                href="about.html"
                                className={`text-gray-700 hover:text-blue-600 transition-colors ${
                                    currentPage === 'about' ? 'text-blue-600 font-medium' : ''
                                }`}
                                onClick={() => setIsMenuOpen(false)}
                            >
                                About
                            </a>
                            <a 
                                href="contact.html"
                                className={`text-gray-700 hover:text-blue-600 transition-colors ${
                                    currentPage === 'contact' ? 'text-blue-600 font-medium' : ''
                                }`}
                                onClick={() => setIsMenuOpen(false)}
                            >
                                Contact
                            </a>
                            
                            <div className="border-t border-gray-200 pt-4 space-y-4">
                                <a 
                                    href="login.html" 
                                    className="block text-gray-700 hover:text-blue-600 transition-colors"
                                    onClick={() => setIsMenuOpen(false)}
                                >
                                    Sign In
                                </a>
                                <a 
                                    href="register.html" 
                                    className="block bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center"
                                    onClick={() => setIsMenuOpen(false)}
                                >
                                    Get Started
                                </a>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </header>
    );
}

// Make component globally available
window.WebsiteHeader = WebsiteHeader;
